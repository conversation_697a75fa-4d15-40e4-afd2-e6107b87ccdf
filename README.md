# Workflow Engine

A lightweight, extensible workflow orchestration engine extracted from Coze Studio.

## Features

- **Visual Workflow Designer**: Drag-and-drop interface for creating workflows
- **Extensible Node System**: Support for custom node types and plugins
- **Real-time Execution**: Live execution monitoring with streaming output
- **Error Handling**: Comprehensive error handling and retry mechanisms
- **State Management**: Persistent workflow state with resume capabilities
- **API-First**: RESTful APIs for all workflow operations

## Architecture

```
workflow-engine/
├── backend/           # Go backend services
│   ├── cmd/          # Application entry points
│   ├── internal/     # Internal packages
│   │   ├── domain/   # Domain models and business logic
│   │   ├── api/      # HTTP handlers and routes
│   │   └── service/  # Business services
│   ├── pkg/          # Shared packages
│   └── configs/      # Configuration files
├── frontend/         # React frontend
│   ├── src/
│   │   ├── components/ # React components
│   │   ├── services/   # API services
│   │   ├── types/      # TypeScript types
│   │   └── utils/      # Utility functions
│   └── public/       # Static assets
├── docs/             # Documentation
└── scripts/          # Build and deployment scripts
```

## Quick Start

### Prerequisites

- Go 1.21+
- Node.js 18+
- Docker (optional)

### Using Make (Recommended)

```bash
# Install dependencies
make install

# Start development environment
make dev

# Build for production
make build

# Run tests
make test
```

### Using Docker

```bash
# Development environment
docker-compose -f docker-compose.dev.yml up

# Production environment
docker-compose up -d
```

### Manual Setup

#### Backend Setup

```bash
cd backend
go mod download
go run cmd/server/main.go
```

#### Frontend Setup

```bash
cd frontend
npm install
npm start
```

## Core Concepts

### Workflow
A workflow is a directed graph of nodes connected by edges, representing a sequence of operations.

### Node
A node represents a single operation or task within a workflow. Each node has:
- Input/Output types
- Configuration parameters
- Execution logic

### Execution
Workflow execution is event-driven and supports:
- Synchronous and asynchronous execution
- Streaming output
- Error handling and retries
- State persistence

## Node Types

### Basic Nodes
- **Entry**: Workflow entry point
- **Exit**: Workflow exit point
- **Selector**: Conditional branching
- **Loop**: Iteration control

### AI Nodes
- **LLM**: Large Language Model integration
- **Text Processor**: Text manipulation and processing

### Data Nodes
- **Variable Assigner**: Variable assignment
- **HTTP Requester**: HTTP API calls
- **Code Runner**: Custom code execution

## API Reference

### Workflow Management
- `POST /api/workflows` - Create workflow
- `GET /api/workflows/{id}` - Get workflow
- `PUT /api/workflows/{id}` - Update workflow
- `DELETE /api/workflows/{id}` - Delete workflow

### Execution
- `POST /api/workflows/{id}/execute` - Execute workflow
- `GET /api/executions/{id}` - Get execution status
- `GET /api/executions/{id}/stream` - Stream execution events

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

MIT License - see LICENSE file for details.
