# Workflow Engine Environment Configuration

# Server Configuration
PORT=8080
HOST=0.0.0.0
ENV=development

# Database Configuration (if using external database)
DB_TYPE=sqlite
DB_HOST=localhost
DB_PORT=5432
DB_NAME=workflow_engine
DB_USER=workflow
DB_PASSWORD=workflow_password
DB_SSL_MODE=disable

# Redis Configuration (if using Redis)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# Security Configuration
JWT_SECRET=your-jwt-secret-key-here
CORS_ORIGINS=http://localhost:3000,http://localhost:8080

# Logging Configuration
LOG_LEVEL=info
LOG_FORMAT=json

# File Storage Configuration
STORAGE_TYPE=local
STORAGE_PATH=./data
# For cloud storage:
# STORAGE_TYPE=s3
# AWS_REGION=us-west-2
# AWS_BUCKET=workflow-engine-storage
# AWS_ACCESS_KEY_ID=your-access-key
# AWS_SECRET_ACCESS_KEY=your-secret-key

# External Service Configuration
# OpenAI API (for LLM nodes)
OPENAI_API_KEY=your-openai-api-key
OPENAI_BASE_URL=https://api.openai.com/v1
OPENAI_TIMEOUT=60s

# Anthropic Claude API
ANTHROPIC_API_KEY=your-anthropic-api-key
ANTHROPIC_BASE_URL=https://api.anthropic.com
ANTHROPIC_TIMEOUT=60s

# Google Gemini API
GOOGLE_API_KEY=your-google-api-key
GOOGLE_BASE_URL=https://generativelanguage.googleapis.com
GOOGLE_TIMEOUT=60s

# Database Connections
MYSQL_DSN=user:password@tcp(localhost:3306)/dbname
POSTGRES_DSN=postgres://user:password@localhost:5432/dbname?sslmode=disable
SQLITE_PATH=./data/workflow.db

# Vector Database (for Knowledge nodes)
PINECONE_API_KEY=your-pinecone-api-key
PINECONE_ENVIRONMENT=your-pinecone-environment
WEAVIATE_URL=http://localhost:8080
WEAVIATE_API_KEY=your-weaviate-api-key

# Email Service (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_FROM=<EMAIL>

# Webhook Configuration
WEBHOOK_TIMEOUT=30s
WEBHOOK_RETRY_COUNT=3

# Rate Limiting
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS_PER_MINUTE=100

# Monitoring and Observability
METRICS_ENABLED=true
METRICS_PORT=9090
TRACING_ENABLED=false
TRACING_ENDPOINT=http://localhost:14268/api/traces

# Development Settings
HOT_RELOAD=true
DEBUG=false
PROFILING_ENABLED=false

# Frontend Configuration (for build-time)
REACT_APP_API_URL=http://localhost:8080
REACT_APP_WS_URL=ws://localhost:8080
REACT_APP_VERSION=1.0.0
