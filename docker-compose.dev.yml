version: '3.8'

services:
  # Backend development service
  backend:
    build:
      context: .
      dockerfile: Dockerfile.dev
      target: backend-dev
    ports:
      - "8080:8080"
    volumes:
      - ./backend:/app/backend
      - backend_cache:/go/pkg/mod
    environment:
      - ENV=development
      - PORT=8080
      - HOT_RELOAD=true
    restart: unless-stopped
    command: ["air", "-c", ".air.toml"]

  # Frontend development service
  frontend:
    build:
      context: .
      dockerfile: Dockerfile.dev
      target: frontend-dev
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app/frontend
      - frontend_cache:/app/frontend/node_modules
    environment:
      - REACT_APP_API_URL=http://localhost:8080
      - CHOKIDAR_USEPOLLING=true
    restart: unless-stopped
    command: ["npm", "start"]

  # Redis for development
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    restart: unless-stopped

volumes:
  backend_cache:
  frontend_cache:
