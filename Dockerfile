# Multi-stage build for Workflow Engine

# Stage 1: Build backend
FROM golang:1.21-alpine AS backend-builder

WORKDIR /app/backend

# Install dependencies
RUN apk add --no-cache git

# Copy go mod files
COPY backend/go.mod backend/go.sum ./

# Download dependencies
RUN go mod download

# Copy source code
COPY backend/ ./

# Build the binary
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o workflow-engine ./cmd/server

# Stage 2: Build frontend
FROM node:18-alpine AS frontend-builder

WORKDIR /app/frontend

# Copy package files
COPY frontend/package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy source code
COPY frontend/ ./

# Build the frontend
RUN npm run build

# Stage 3: Final image
FROM alpine:latest

# Install ca-certificates for HTTPS requests
RUN apk --no-cache add ca-certificates

WORKDIR /app

# Create non-root user
RUN addgroup -g 1001 -S workflow && \
    adduser -S workflow -u 1001

# Copy backend binary
COPY --from=backend-builder /app/backend/workflow-engine .

# Copy frontend build
COPY --from=frontend-builder /app/frontend/build ./frontend

# Create necessary directories
RUN mkdir -p /app/data && \
    chown -R workflow:workflow /app

# Switch to non-root user
USER workflow

# Expose port
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost:8080/health || exit 1

# Run the application
CMD ["./workflow-engine"]
