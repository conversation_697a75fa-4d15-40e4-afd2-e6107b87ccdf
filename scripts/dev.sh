#!/bin/bash

# Workflow Engine Development Script

set -e

echo "🚀 Starting Workflow Engine in development mode..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "README.md" ]; then
    print_error "Please run this script from the workflow-engine root directory"
    exit 1
fi

# Function to cleanup background processes
cleanup() {
    print_status "Shutting down services..."
    if [ ! -z "$BACKEND_PID" ]; then
        kill $BACKEND_PID 2>/dev/null || true
    fi
    if [ ! -z "$FRONTEND_PID" ]; then
        kill $FRONTEND_PID 2>/dev/null || true
    fi
    exit 0
}

# Set up signal handlers
trap cleanup SIGINT SIGTERM

# Start backend in development mode
print_status "Starting backend server..."
cd backend

# Check if Go is installed
if ! command -v go &> /dev/null; then
    print_error "Go is not installed. Please install Go 1.21 or later."
    exit 1
fi

# Download dependencies if needed
if [ ! -f "go.sum" ]; then
    print_status "Downloading Go dependencies..."
    go mod download
fi

# Start backend server in background
go run ./cmd/server &
BACKEND_PID=$!
print_status "Backend server started (PID: $BACKEND_PID)"

# Start frontend in development mode
cd ../frontend
print_status "Starting frontend development server..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    print_error "Node.js is not installed. Please install Node.js 18 or later."
    exit 1
fi

# Install dependencies if needed
if [ ! -d "node_modules" ]; then
    print_status "Installing frontend dependencies..."
    npm install
fi

# Start frontend development server in background
npm start &
FRONTEND_PID=$!
print_status "Frontend development server started (PID: $FRONTEND_PID)"

print_status "🎉 Development environment is ready!"
print_status "Backend API: http://localhost:8080"
print_status "Frontend: http://localhost:3000"
print_status "Press Ctrl+C to stop all services"

# Wait for processes
wait
