#!/bin/bash

# Workflow Engine Build Script

set -e

echo "🚀 Building Workflow Engine..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "README.md" ]; then
    print_error "Please run this script from the workflow-engine root directory"
    exit 1
fi

# Build backend
print_status "Building backend..."
cd backend

# Check if Go is installed
if ! command -v go &> /dev/null; then
    print_error "Go is not installed. Please install Go 1.21 or later."
    exit 1
fi

# Download dependencies
print_status "Downloading Go dependencies..."
go mod download

# Run tests
print_status "Running backend tests..."
go test ./... || {
    print_warning "Some tests failed, but continuing with build..."
}

# Build the binary
print_status "Building backend binary..."
CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o bin/workflow-engine ./cmd/server

print_status "Backend build completed ✅"

# Build frontend
cd ../frontend
print_status "Building frontend..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    print_error "Node.js is not installed. Please install Node.js 18 or later."
    exit 1
fi

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    print_error "npm is not installed. Please install npm."
    exit 1
fi

# Install dependencies
print_status "Installing frontend dependencies..."
npm install

# Run type checking
print_status "Running TypeScript type checking..."
npm run type-check || {
    print_warning "Type checking failed, but continuing with build..."
}

# Run linting
print_status "Running ESLint..."
npm run lint || {
    print_warning "Linting failed, but continuing with build..."
}

# Build the frontend
print_status "Building frontend bundle..."
npm run build

print_status "Frontend build completed ✅"

# Create distribution directory
cd ..
print_status "Creating distribution package..."

mkdir -p dist
cp -r backend/bin dist/
cp -r frontend/build dist/frontend
cp README.md dist/
cp -r docs dist/ 2>/dev/null || print_warning "No docs directory found"

# Create version file
echo "$(date '+%Y-%m-%d %H:%M:%S')" > dist/VERSION
echo "Built on: $(hostname)" >> dist/VERSION
echo "Git commit: $(git rev-parse --short HEAD 2>/dev/null || echo 'unknown')" >> dist/VERSION

print_status "Distribution package created in ./dist/ ✅"

# Create Docker image if Docker is available
if command -v docker &> /dev/null; then
    print_status "Building Docker image..."
    docker build -t workflow-engine:latest . || {
        print_warning "Docker build failed, but main build is complete"
    }
    print_status "Docker image built ✅"
else
    print_warning "Docker not found, skipping Docker image build"
fi

print_status "🎉 Build completed successfully!"
print_status "Backend binary: ./dist/bin/workflow-engine"
print_status "Frontend files: ./dist/frontend/"
print_status "To run: cd dist && ./bin/workflow-engine"
