# Workflow Engine Makefile

.PHONY: help build dev test clean docker docker-dev install deps backend frontend

# Default target
help:
	@echo "Workflow Engine - Available commands:"
	@echo ""
	@echo "Development:"
	@echo "  dev          - Start development environment"
	@echo "  install      - Install all dependencies"
	@echo "  deps         - Download dependencies"
	@echo ""
	@echo "Building:"
	@echo "  build        - Build production binaries"
	@echo "  backend      - Build backend only"
	@echo "  frontend     - Build frontend only"
	@echo ""
	@echo "Testing:"
	@echo "  test         - Run all tests"
	@echo "  test-backend - Run backend tests"
	@echo "  test-frontend- Run frontend tests"
	@echo ""
	@echo "Docker:"
	@echo "  docker       - Build and run with Docker"
	@echo "  docker-dev   - Run development environment with Docker"
	@echo ""
	@echo "Utilities:"
	@echo "  clean        - Clean build artifacts"
	@echo "  lint         - Run linters"
	@echo "  format       - Format code"

# Development
dev:
	@echo "🚀 Starting development environment..."
	@chmod +x scripts/dev.sh
	@./scripts/dev.sh

install: deps

deps:
	@echo "📦 Installing dependencies..."
	@cd backend && go mod download
	@cd frontend && npm install

# Building
build:
	@echo "🔨 Building production binaries..."
	@chmod +x scripts/build.sh
	@./scripts/build.sh

backend:
	@echo "🔨 Building backend..."
	@cd backend && mkdir -p bin && go build -o bin/workflow-engine ./cmd/server

frontend:
	@echo "🔨 Building frontend..."
	@cd frontend && npm run build

# Testing
test: test-backend test-frontend

test-backend:
	@echo "🧪 Running backend tests..."
	@cd backend && go test ./...

test-frontend:
	@echo "🧪 Running frontend tests..."
	@cd frontend && npm test -- --watchAll=false

# Docker
docker:
	@echo "🐳 Building and running with Docker..."
	@docker-compose up --build

docker-dev:
	@echo "🐳 Running development environment with Docker..."
	@docker-compose -f docker-compose.dev.yml up --build

# Utilities
clean:
	@echo "🧹 Cleaning build artifacts..."
	@rm -rf backend/bin
	@rm -rf frontend/build
	@rm -rf dist
	@docker system prune -f

lint:
	@echo "🔍 Running linters..."
	@cd backend && go vet ./...
	@cd frontend && npm run lint

format:
	@echo "✨ Formatting code..."
	@cd backend && go fmt ./...
	@cd frontend && npm run lint:fix

# Quick start
start: install build
	@echo "🚀 Starting Workflow Engine..."
	@cd dist && ./bin/workflow-engine

# Production deployment
deploy:
	@echo "🚀 Deploying to production..."
	@docker-compose -f docker-compose.yml up -d

# Stop services
stop:
	@echo "🛑 Stopping services..."
	@docker-compose down

# View logs
logs:
	@docker-compose logs -f

# Database migration (if using database)
migrate:
	@echo "🗄️ Running database migrations..."
	@cd backend && go run ./cmd/migrate

# Generate API documentation
docs:
	@echo "📚 Generating API documentation..."
	@cd backend && swag init -g ./cmd/server/main.go

# Security scan
security:
	@echo "🔒 Running security scan..."
	@cd backend && gosec ./...
	@cd frontend && npm audit

# Performance test
perf:
	@echo "⚡ Running performance tests..."
	@echo "Performance tests not implemented yet"

# Backup data
backup:
	@echo "💾 Creating backup..."
	@docker-compose exec workflow-engine tar -czf /tmp/backup-$(shell date +%Y%m%d-%H%M%S).tar.gz /app/data

# Health check
health:
	@echo "🏥 Checking service health..."
	@curl -f http://localhost:8080/health || echo "Service is not healthy"
