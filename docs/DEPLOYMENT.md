# Workflow Engine Deployment Guide

This guide covers different deployment options for the Workflow Engine.

## Prerequisites

- <PERSON><PERSON> and Docker Compose (recommended)
- Go 1.21+ (for building from source)
- Node.js 18+ (for building frontend)

## Quick Start with Docker

### 1. Using Docker Compose (Recommended)

```bash
# Clone the repository
git clone <repository-url>
cd workflow-engine

# Copy environment file
cp .env.example .env

# Edit environment variables as needed
nano .env

# Start the services
docker-compose up -d

# Check status
docker-compose ps
```

The application will be available at:
- Frontend: http://localhost:8080
- API: http://localhost:8080/api/v1
- Health check: http://localhost:8080/health

### 2. Using Docker only

```bash
# Build the image
docker build -t workflow-engine .

# Run the container
docker run -d \
  --name workflow-engine \
  -p 8080:8080 \
  -v workflow_data:/app/data \
  workflow-engine
```

## Building from Source

### 1. Build Script

```bash
# Make build script executable
chmod +x scripts/build.sh

# Run build
./scripts/build.sh

# Start the application
cd dist
./bin/workflow-engine
```

### 2. Manual Build

```bash
# Build backend
cd backend
go mod download
go build -o bin/workflow-engine ./cmd/server

# Build frontend
cd ../frontend
npm install
npm run build

# Copy files
mkdir -p ../dist
cp -r backend/bin ../dist/
cp -r frontend/build ../dist/frontend
```

## Development Environment

### Using Make

```bash
# Install dependencies
make install

# Start development environment
make dev
```

### Using Scripts

```bash
# Start development environment
chmod +x scripts/dev.sh
./scripts/dev.sh
```

### Using Docker Compose

```bash
# Start development environment with Docker
docker-compose -f docker-compose.dev.yml up
```

## Production Deployment

### 1. Environment Configuration

Create a `.env` file with production settings:

```bash
# Server
ENV=production
PORT=8080
HOST=0.0.0.0

# Database (if using external database)
DB_TYPE=postgres
DB_HOST=your-db-host
DB_PORT=5432
DB_NAME=workflow_engine
DB_USER=workflow_user
DB_PASSWORD=secure_password

# Security
JWT_SECRET=your-secure-jwt-secret
CORS_ORIGINS=https://your-domain.com

# Logging
LOG_LEVEL=info
LOG_FORMAT=json
```

### 2. Using Docker Compose

```bash
# Production deployment
docker-compose -f docker-compose.yml up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

### 3. Using Kubernetes

Create Kubernetes manifests:

```yaml
# deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: workflow-engine
spec:
  replicas: 3
  selector:
    matchLabels:
      app: workflow-engine
  template:
    metadata:
      labels:
        app: workflow-engine
    spec:
      containers:
      - name: workflow-engine
        image: workflow-engine:latest
        ports:
        - containerPort: 8080
        env:
        - name: ENV
          value: "production"
        - name: PORT
          value: "8080"
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
---
apiVersion: v1
kind: Service
metadata:
  name: workflow-engine-service
spec:
  selector:
    app: workflow-engine
  ports:
  - port: 80
    targetPort: 8080
  type: LoadBalancer
```

Deploy to Kubernetes:

```bash
kubectl apply -f deployment.yaml
```

## Monitoring and Maintenance

### Health Checks

The application provides health check endpoints:

```bash
# Basic health check
curl http://localhost:8080/health

# Detailed health check (if implemented)
curl http://localhost:8080/health/detailed
```

### Logs

```bash
# Docker Compose logs
docker-compose logs -f workflow-engine

# Docker logs
docker logs -f workflow-engine

# Application logs (if running directly)
tail -f /app/logs/application.log
```

### Backup

```bash
# Backup data volume
docker run --rm -v workflow_data:/data -v $(pwd):/backup alpine tar czf /backup/backup.tar.gz /data

# Restore data
docker run --rm -v workflow_data:/data -v $(pwd):/backup alpine tar xzf /backup/backup.tar.gz -C /
```

### Updates

```bash
# Pull latest images
docker-compose pull

# Restart services
docker-compose up -d

# Clean up old images
docker image prune -f
```

## Scaling

### Horizontal Scaling

The application is stateless and can be scaled horizontally:

```bash
# Scale with Docker Compose
docker-compose up -d --scale workflow-engine=3

# Scale with Kubernetes
kubectl scale deployment workflow-engine --replicas=5
```

### Load Balancing

Use a reverse proxy like Nginx or a cloud load balancer:

```nginx
upstream workflow_engine {
    server localhost:8080;
    server localhost:8081;
    server localhost:8082;
}

server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://workflow_engine;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## Security Considerations

1. **Environment Variables**: Never commit sensitive data to version control
2. **HTTPS**: Always use HTTPS in production
3. **Authentication**: Implement proper authentication and authorization
4. **Network Security**: Use firewalls and network policies
5. **Regular Updates**: Keep dependencies and base images updated
6. **Secrets Management**: Use proper secrets management solutions

## Troubleshooting

### Common Issues

1. **Port conflicts**: Change the port in docker-compose.yml or .env
2. **Permission issues**: Ensure proper file permissions
3. **Memory issues**: Increase Docker memory limits
4. **Database connection**: Check database configuration and connectivity

### Debug Mode

Enable debug mode for troubleshooting:

```bash
# Set environment variable
export DEBUG=true

# Or in .env file
DEBUG=true
```

### Performance Tuning

1. **Database**: Optimize database queries and indexes
2. **Caching**: Implement Redis caching
3. **Resource Limits**: Set appropriate CPU and memory limits
4. **Connection Pooling**: Configure database connection pooling

## Support

For issues and questions:
1. Check the logs for error messages
2. Review the configuration
3. Consult the API documentation
4. Create an issue in the repository
