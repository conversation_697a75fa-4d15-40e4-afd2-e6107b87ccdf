# Workflow Engine API Documentation

Base URL: `http://localhost:8080/api/v1`

## Authentication

Currently, the API does not require authentication. In production, you should implement proper authentication and authorization.

## Error Responses

All endpoints return errors in the following format:

```json
{
  "error": "Error message description"
}
```

Common HTTP status codes:
- `200` - Success
- `201` - Created
- `400` - Bad Request
- `404` - Not Found
- `500` - Internal Server Error

## Workflows

### Create Workflow

Create a new workflow.

**POST** `/workflows`

**Request Body:**
```json
{
  "name": "My Workflow",
  "description": "A sample workflow"
}
```

**Response:**
```json
{
  "id": "workflow-uuid",
  "message": "Workflow created successfully"
}
```

### Get Workflow

Retrieve a workflow by ID.

**GET** `/workflows/{id}`

**Response:**
```json
{
  "id": "workflow-uuid",
  "name": "My Workflow",
  "description": "A sample workflow",
  "version": "1.0.0",
  "status": "draft",
  "nodes": [
    {
      "key": "entry-1",
      "name": "Entry",
      "type": "entry",
      "position": { "x": 100, "y": 100 },
      "config": {},
      "input_types": {},
      "output_types": {
        "output": { "type": "any", "description": "Any type of value" }
      }
    }
  ],
  "connections": [],
  "created_at": "2023-12-01T10:00:00Z",
  "updated_at": "2023-12-01T10:00:00Z"
}
```

### Update Workflow

Update an existing workflow.

**PUT** `/workflows/{id}`

**Request Body:**
```json
{
  "name": "Updated Workflow Name",
  "description": "Updated description",
  "nodes": [
    {
      "key": "entry-1",
      "name": "Entry",
      "type": "entry",
      "position": { "x": 100, "y": 100 },
      "config": {}
    }
  ],
  "connections": [
    {
      "id": "conn-1",
      "from_node": "entry-1",
      "to_node": "exit-1"
    }
  ]
}
```

**Response:**
```json
{
  "message": "Workflow updated successfully"
}
```

### Delete Workflow

Delete a workflow by ID.

**DELETE** `/workflows/{id}`

**Response:**
```json
{
  "message": "Workflow deleted successfully"
}
```

### List Workflows

List all workflows with pagination.

**GET** `/workflows`

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 10, max: 100)
- `status` (optional): Filter by status (draft, published, archived)

**Response:**
```json
{
  "workflows": [
    {
      "id": "workflow-uuid",
      "name": "My Workflow",
      "description": "A sample workflow",
      "version": "1.0.0",
      "status": "draft",
      "created_at": "2023-12-01T10:00:00Z",
      "updated_at": "2023-12-01T10:00:00Z"
    }
  ],
  "total": 1,
  "page": 1,
  "limit": 10
}
```

### Validate Workflow

Validate a workflow schema.

**POST** `/workflows/validate`

**Request Body:**
```json
{
  "id": "workflow-uuid",
  "name": "My Workflow",
  "nodes": [...],
  "connections": [...]
}
```

**Response:**
```json
{
  "valid": true,
  "message": "Workflow schema is valid"
}
```

Or if invalid:
```json
{
  "valid": false,
  "error": "Workflow must have an entry node"
}
```

### Execute Workflow

Execute a workflow with input data.

**POST** `/workflows/{id}/execute`

**Request Body:**
```json
{
  "input": {
    "message": "Hello, World!",
    "user_id": "123"
  },
  "options": {
    "async": false,
    "timeout_seconds": 300,
    "enable_streaming": true,
    "enable_debug": false
  }
}
```

**Response:**
```json
{
  "id": "execution-uuid",
  "workflow_id": "workflow-uuid",
  "version": "1.0.0",
  "status": "success",
  "input": {
    "message": "Hello, World!",
    "user_id": "123"
  },
  "output": {
    "result": "Processed: Hello, World!"
  },
  "started_at": "2023-12-01T10:00:00Z",
  "completed_at": "2023-12-01T10:00:30Z",
  "duration": 30000,
  "node_executions": [
    {
      "id": "node-exec-uuid",
      "node_key": "entry-1",
      "node_name": "Entry",
      "node_type": "entry",
      "status": "success",
      "input": { "message": "Hello, World!" },
      "output": { "message": "Hello, World!" },
      "started_at": "2023-12-01T10:00:00Z",
      "completed_at": "2023-12-01T10:00:01Z",
      "duration": 1000
    }
  ]
}
```

## Executions

### Get Execution

Retrieve an execution by ID.

**GET** `/executions/{id}`

**Response:**
```json
{
  "id": "execution-uuid",
  "workflow_id": "workflow-uuid",
  "version": "1.0.0",
  "status": "running",
  "input": { "message": "Hello, World!" },
  "started_at": "2023-12-01T10:00:00Z",
  "node_executions": [...]
}
```

### Cancel Execution

Cancel a running execution.

**DELETE** `/executions/{id}`

**Response:**
```json
{
  "message": "Execution cancelled successfully"
}
```

### Get Execution Events

Stream execution events via Server-Sent Events (SSE).

**GET** `/executions/{id}/events`

**Response:** Server-Sent Events stream

```
event: event
data: {"id":"event-uuid","execution_id":"execution-uuid","type":"workflow_started","timestamp":"2023-12-01T10:00:00Z","data":{"workflow_id":"workflow-uuid","input":{"message":"Hello, World!"}}}

event: event
data: {"id":"event-uuid","execution_id":"execution-uuid","type":"node_started","timestamp":"2023-12-01T10:00:01Z","data":{"node_key":"entry-1","node_type":"entry","input":{"message":"Hello, World!"}}}

event: event
data: {"id":"event-uuid","execution_id":"execution-uuid","type":"node_completed","timestamp":"2023-12-01T10:00:02Z","data":{"node_key":"entry-1","output":{"message":"Hello, World!"}}}

event: event
data: {"id":"event-uuid","execution_id":"execution-uuid","type":"workflow_completed","timestamp":"2023-12-01T10:00:30Z","data":{"output":{"result":"Processed: Hello, World!"}}}
```

## Health Check

### Health Status

Check the health status of the service.

**GET** `/health`

**Response:**
```json
{
  "status": "ok",
  "service": "workflow-engine"
}
```

## Node Types

The following node types are supported:

### Entry Node
- **Type:** `entry`
- **Description:** Workflow entry point
- **Inputs:** None
- **Outputs:** `output` (any)

### Exit Node
- **Type:** `exit`
- **Description:** Workflow exit point
- **Inputs:** `input` (any)
- **Outputs:** None

### Selector Node
- **Type:** `selector`
- **Description:** Conditional branching
- **Inputs:** `input` (any)
- **Outputs:** `branch` (number), `matched` (boolean), `condition` (object)
- **Config:**
  ```json
  {
    "conditions": [
      {
        "field": "status",
        "operator": "equals",
        "value": "active"
      }
    ]
  }
  ```

### Variable Assigner Node
- **Type:** `variable_assigner`
- **Description:** Assign values to variables
- **Inputs:** `input` (any)
- **Outputs:** `output` (any)
- **Config:**
  ```json
  {
    "assignments": [
      {
        "variable": "result",
        "type": "constant",
        "value": "Hello, World!"
      }
    ]
  }
  ```

### Text Processor Node
- **Type:** `text_processor`
- **Description:** Process and manipulate text
- **Inputs:** `text` (string)
- **Outputs:** `result` (any)
- **Config:**
  ```json
  {
    "operation": "uppercase",
    "input_field": "text",
    "output_field": "result"
  }
  ```

### LLM Node
- **Type:** `llm`
- **Description:** Large Language Model processing
- **Inputs:** `prompt` (string), `context` (object)
- **Outputs:** `response` (string), `prompt` (string)
- **Config:**
  ```json
  {
    "prompt": "Process this text: {{input}}",
    "model": "gpt-3.5-turbo",
    "temperature": 0.7,
    "max_tokens": 1000
  }
  ```

### HTTP Requester Node
- **Type:** `http_requester`
- **Description:** Make HTTP requests
- **Inputs:** `url` (string), `headers` (object), `body` (any)
- **Outputs:** `http_response` (object)
- **Config:**
  ```json
  {
    "url": "https://api.example.com/data",
    "method": "POST",
    "headers": {
      "Content-Type": "application/json"
    },
    "timeout": 30
  }
  ```

### Code Runner Node
- **Type:** `code_runner`
- **Description:** Execute custom code
- **Inputs:** `input` (any)
- **Outputs:** `code_result` (any), `language` (string)
- **Config:**
  ```json
  {
    "language": "javascript",
    "code": "return { result: input.value * 2 };",
    "timeout": 30
  }
  ```

## WebSocket Events

For real-time updates, the API supports WebSocket connections at `/ws`.

### Connection

```javascript
const ws = new WebSocket('ws://localhost:8080/ws');

ws.onmessage = function(event) {
  const data = JSON.parse(event.data);
  console.log('Received:', data);
};
```

### Event Types

- `execution_started` - Workflow execution started
- `execution_completed` - Workflow execution completed
- `execution_failed` - Workflow execution failed
- `node_started` - Node execution started
- `node_completed` - Node execution completed
- `node_failed` - Node execution failed
