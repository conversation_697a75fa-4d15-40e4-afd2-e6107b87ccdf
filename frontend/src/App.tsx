import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { CssBaseline, Box } from '@mui/material';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

import WorkflowEditor from './components/WorkflowEditor/WorkflowEditor';
import WorkflowList from './components/WorkflowList/WorkflowList';
import Header from './components/Layout/Header';

// Create theme
const theme = createTheme({
  palette: {
    mode: 'light',
    primary: {
      main: '#1976d2',
    },
    secondary: {
      main: '#dc004e',
    },
    background: {
      default: '#f5f5f5',
    },
  },
  typography: {
    fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
  },
});

// Create query client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
    },
  },
});

const App: React.FC = () => {
  return (
    <QueryClientProvider client={queryClient}>
      <ThemeProvider theme={theme}>
        <CssBaseline />
        <Router>
          <Box display="flex" flexDirection="column" height="100vh">
            <Header />
            <Box flex={1} overflow="hidden">
              <Routes>
                <Route path="/" element={<Navigate to="/workflows" replace />} />
                <Route path="/workflows" element={<WorkflowList />} />
                <Route path="/workflows/:id" element={<WorkflowEditor />} />
                <Route path="/workflows/:id/edit" element={<WorkflowEditor />} />
                <Route path="*" element={<Navigate to="/workflows" replace />} />
              </Routes>
            </Box>
          </Box>
        </Router>
      </ThemeProvider>
    </QueryClientProvider>
  );
};

export default App;
