import React from 'react';
import {
  Box,
  Typography,
  TextField,
  Paper,
  Divider,
  IconButton,
} from '@mui/material';
import DeleteIcon from '@mui/icons-material/Delete';
import { useWorkflowStore } from '../../stores/workflowStore';
import { nodeConfigs } from '../nodes/nodeConfigs';

const NodePropertiesPanel: React.FC = () => {
  const { selectedNodes, getNode, updateNode, removeNode } = useWorkflowStore();

  if (selectedNodes.length === 0) {
    return (
      <Box sx={{ p: 2 }}>
        <Typography variant="h6" gutterBottom>
          Properties
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Select a node to edit its properties
        </Typography>
      </Box>
    );
  }

  if (selectedNodes.length > 1) {
    return (
      <Box sx={{ p: 2 }}>
        <Typography variant="h6" gutterBottom>
          Properties
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Multiple nodes selected ({selectedNodes.length} nodes)
        </Typography>
      </Box>
    );
  }

  const nodeKey = selectedNodes[0];
  const node = getNode(nodeKey);

  if (!node) {
    return (
      <Box sx={{ p: 2 }}>
        <Typography variant="h6" gutterBottom>
          Properties
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Node not found
        </Typography>
      </Box>
    );
  }

  const config = nodeConfigs[node.type];

  const handleNameChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    updateNode(nodeKey, { name: event.target.value });
  };

  const handleDescriptionChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    updateNode(nodeKey, { description: event.target.value });
  };

  const handleDelete = () => {
    removeNode(nodeKey);
  };

  return (
    <Box sx={{ height: '100%', overflow: 'auto', p: 2 }}>
      <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
        <Typography variant="h6">
          Properties
        </Typography>
        <IconButton onClick={handleDelete} color="error" size="small">
          <DeleteIcon />
        </IconButton>
      </Box>

      <Paper sx={{ p: 2, mb: 2 }}>
        <Box display="flex" alignItems="center" gap={1} mb={2}>
          <Typography sx={{ fontSize: '1.5em' }}>
            {config.icon}
          </Typography>
          <Box>
            <Typography variant="subtitle1" fontWeight={600}>
              {config.label}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              {config.category}
            </Typography>
          </Box>
        </Box>
        
        <Typography variant="body2" color="text.secondary">
          {config.description}
        </Typography>
      </Paper>

      <Box sx={{ mb: 2 }}>
        <TextField
          fullWidth
          label="Name"
          value={node.name}
          onChange={handleNameChange}
          size="small"
          sx={{ mb: 2 }}
        />
        
        <TextField
          fullWidth
          label="Description"
          value={node.description || ''}
          onChange={handleDescriptionChange}
          size="small"
          multiline
          rows={2}
        />
      </Box>

      <Divider sx={{ my: 2 }} />

      <Typography variant="subtitle2" gutterBottom>
        Node Information
      </Typography>
      
      <Box sx={{ mb: 2 }}>
        <Typography variant="caption" color="text.secondary">
          Type: {node.type}
        </Typography>
        <br />
        <Typography variant="caption" color="text.secondary">
          Key: {node.key}
        </Typography>
      </Box>

      {/* Input Types */}
      {Object.keys(config.inputs).length > 0 && (
        <>
          <Typography variant="subtitle2" gutterBottom>
            Inputs
          </Typography>
          <Box sx={{ mb: 2 }}>
            {Object.entries(config.inputs).map(([key, type]) => (
              <Box key={key} sx={{ mb: 1 }}>
                <Typography variant="body2" fontWeight={500}>
                  {key}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  {type.type} - {type.description}
                </Typography>
              </Box>
            ))}
          </Box>
        </>
      )}

      {/* Output Types */}
      {Object.keys(config.outputs).length > 0 && (
        <>
          <Typography variant="subtitle2" gutterBottom>
            Outputs
          </Typography>
          <Box sx={{ mb: 2 }}>
            {Object.entries(config.outputs).map(([key, type]) => (
              <Box key={key} sx={{ mb: 1 }}>
                <Typography variant="body2" fontWeight={500}>
                  {key}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  {type.type} - {type.description}
                </Typography>
              </Box>
            ))}
          </Box>
        </>
      )}

      {/* Configuration */}
      <Typography variant="subtitle2" gutterBottom>
        Configuration
      </Typography>
      <Paper sx={{ p: 2, backgroundColor: 'grey.50' }}>
        <Typography variant="caption" color="text.secondary">
          Configuration editor coming soon...
        </Typography>
      </Paper>
    </Box>
  );
};

export default NodePropertiesPanel;
