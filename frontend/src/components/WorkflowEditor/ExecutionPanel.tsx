import React from 'react';
import {
  Box,
  Typography,
  LinearProgress,
  Chip,
  Icon<PERSON>utton,
  Tooltip,
} from '@mui/material';
import StopIcon from '@mui/icons-material/Stop';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import { useExecutionStore } from '../../stores/executionStore';
import { ExecutionStatus } from '../../types/workflow';

const ExecutionPanel: React.FC = () => {
  const { execution, isRunning, getExecutionProgress } = useExecutionStore();

  if (!execution) {
    return null;
  }

  const progress = getExecutionProgress();
  
  const getStatusColor = (status: ExecutionStatus) => {
    switch (status) {
      case ExecutionStatus.RUNNING:
        return 'primary';
      case ExecutionStatus.SUCCESS:
        return 'success';
      case ExecutionStatus.FAILED:
        return 'error';
      case ExecutionStatus.CANCELLED:
        return 'warning';
      default:
        return 'default';
    }
  };

  const getStatusIcon = (status: ExecutionStatus) => {
    switch (status) {
      case ExecutionStatus.RUNNING:
        return '⏳';
      case ExecutionStatus.SUCCESS:
        return '✅';
      case ExecutionStatus.FAILED:
        return '❌';
      case ExecutionStatus.CANCELLED:
        return '⏹️';
      default:
        return '⚪';
    }
  };

  const formatDuration = (ms: number) => {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  };

  return (
    <Box sx={{ minWidth: 250 }}>
      <Box display="flex" alignItems="center" justifyContent="space-between" mb={1}>
        <Typography variant="subtitle2" fontWeight={600}>
          Execution Status
        </Typography>
        {isRunning && (
          <Tooltip title="Stop execution">
            <IconButton size="small" color="error">
              <StopIcon fontSize="small" />
            </IconButton>
          </Tooltip>
        )}
      </Box>

      <Box display="flex" alignItems="center" gap={1} mb={2}>
        <Typography sx={{ fontSize: '1.2em' }}>
          {getStatusIcon(execution.status)}
        </Typography>
        <Chip
          label={execution.status.toUpperCase()}
          color={getStatusColor(execution.status)}
          size="small"
        />
      </Box>

      {/* Progress Bar */}
      {isRunning && (
        <Box sx={{ mb: 2 }}>
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={0.5}>
            <Typography variant="caption">
              Progress
            </Typography>
            <Typography variant="caption">
              {progress.completed}/{progress.total} nodes
            </Typography>
          </Box>
          <LinearProgress
            variant="determinate"
            value={progress.percentage}
            sx={{ height: 6, borderRadius: 3 }}
          />
        </Box>
      )}

      {/* Execution Info */}
      <Box sx={{ mb: 2 }}>
        <Typography variant="caption" color="text.secondary" display="block">
          ID: {execution.id.slice(0, 8)}...
        </Typography>
        <Typography variant="caption" color="text.secondary" display="block">
          Started: {new Date(execution.started_at).toLocaleTimeString()}
        </Typography>
        {execution.completed_at && (
          <Typography variant="caption" color="text.secondary" display="block">
            Duration: {formatDuration(execution.duration || 0)}
          </Typography>
        )}
      </Box>

      {/* Token Usage */}
      {execution.token_usage && (
        <Box sx={{ mb: 2 }}>
          <Typography variant="caption" fontWeight={500} display="block">
            Token Usage
          </Typography>
          <Typography variant="caption" color="text.secondary" display="block">
            Input: {execution.token_usage.input_tokens}
          </Typography>
          <Typography variant="caption" color="text.secondary" display="block">
            Output: {execution.token_usage.output_tokens}
          </Typography>
          <Typography variant="caption" color="text.secondary" display="block">
            Total: {execution.token_usage.input_tokens + execution.token_usage.output_tokens}
          </Typography>
        </Box>
      )}

      {/* Error Message */}
      {execution.error && (
        <Box sx={{ p: 1, backgroundColor: 'error.light', borderRadius: 1, mb: 2 }}>
          <Typography variant="caption" color="error.contrastText">
            Error: {execution.error}
          </Typography>
        </Box>
      )}

      {/* Output */}
      {execution.output && execution.status === ExecutionStatus.SUCCESS && (
        <Box sx={{ p: 1, backgroundColor: 'success.light', borderRadius: 1 }}>
          <Typography variant="caption" fontWeight={500} display="block" color="success.contrastText">
            Output:
          </Typography>
          <Typography variant="caption" color="success.contrastText">
            {typeof execution.output === 'string' 
              ? execution.output 
              : JSON.stringify(execution.output, null, 2)
            }
          </Typography>
        </Box>
      )}
    </Box>
  );
};

export default ExecutionPanel;
