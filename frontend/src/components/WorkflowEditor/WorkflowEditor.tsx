import React, { useCallback, useEffect, useRef } from 'react';
import React<PERSON>low, {
  Node,
  Edge,
  addEdge,
  useNodesState,
  useEdgesState,
  Controls,
  MiniMap,
  Background,
  Connection,
  ConnectionMode,
  Panel,
} from 'reactflow';
import 'reactflow/dist/style.css';

import { Box, Paper, Typography } from '@mui/material';
import { useParams } from 'react-router-dom';
import { useWorkflowStore } from '../../stores/workflowStore';
import { useExecutionStore } from '../../stores/executionStore';
import { NodeType, NodeSchema, Connection as WorkflowConnection, WorkflowSchema, WorkflowStatus } from '../../types/workflow';
import { nodeConfigs } from '../nodes/nodeConfigs';
import NodePalette from './NodePalette';
import NodePropertiesPanel from './NodePropertiesPanel';
import ExecutionPanel from './ExecutionPanel';
import Toolbar from './Toolbar';

// Custom node components
import CustomNode from '../nodes/CustomNode';

const nodeTypes = {
  custom: CustomNode,
};

interface WorkflowEditorProps {
  workflowId?: string;
  readOnly?: boolean;
}

const WorkflowEditor: React.FC<WorkflowEditorProps> = ({ workflowId, readOnly = false }) => {
  const { id } = useParams<{ id: string }>();
  const currentWorkflowId = workflowId || id;

  const reactFlowWrapper = useRef<HTMLDivElement>(null);
  const [nodes, setNodes, onNodesChange] = useNodesState([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState([]);

  const {
    workflow,
    selectedNodes,
    addNode,
    updateNode,
    removeNode,
    addConnection,
    removeConnection,
    selectNode,
    clearSelection,
    setViewport,
    setZoom,
    setWorkflow,
  } = useWorkflowStore();

  const {
    execution,
    getNodeExecutionStatus,
  } = useExecutionStore();

  // Initialize workflow if none exists
  useEffect(() => {
    if (!workflow && currentWorkflowId) {
      // Create a new empty workflow
      const newWorkflow: WorkflowSchema = {
        id: currentWorkflowId,
        name: 'New Workflow',
        description: 'A new workflow',
        version: '1.0.0',
        nodes: [
          {
            key: 'entry',
            name: 'Start',
            type: NodeType.ENTRY,
            description: 'Workflow entry point',
            config: {},
            position: { x: 100, y: 200 },
          },
          {
            key: 'llm',
            name: 'AI Assistant',
            type: NodeType.LLM,
            description: 'Process text with AI',
            config: {
              model: 'gpt-4o-mini',
              system_prompt: 'You are a helpful AI assistant.',
              user_prompt: 'Please respond to: {{text}}',
              temperature: 0.7,
              max_tokens: 500,
            },
            position: { x: 300, y: 200 },
          },
          {
            key: 'transform',
            name: 'Transform Data',
            type: NodeType.DATA_TRANSFORMER,
            description: 'Transform the AI response',
            config: {
              transform_type: 'uppercase',
              source_field: 'response',
              target_field: 'response_upper',
            },
            position: { x: 500, y: 200 },
          },
          {
            key: 'notification',
            name: 'Send Notification',
            type: NodeType.NOTIFICATION,
            description: 'Send success notification',
            config: {
              type: 'success',
              message: 'Workflow completed: {{response_upper}}',
              channels: ['console'],
            },
            position: { x: 700, y: 200 },
          },
          {
            key: 'exit',
            name: 'End',
            type: NodeType.EXIT,
            description: 'Workflow exit point',
            config: {},
            position: { x: 900, y: 200 },
          },
        ],
        connections: [
          {
            id: 'entry-llm',
            from_node: 'entry',
            to_node: 'llm',
          },
          {
            id: 'llm-transform',
            from_node: 'llm',
            to_node: 'transform',
          },
          {
            id: 'transform-notification',
            from_node: 'transform',
            to_node: 'notification',
          },
          {
            id: 'notification-exit',
            from_node: 'notification',
            to_node: 'exit',
          },
        ],
        status: WorkflowStatus.DRAFT,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };
      setWorkflow(newWorkflow);
    }
  }, [currentWorkflowId, workflow, setWorkflow]);

  // Convert workflow schema to ReactFlow format
  const convertToReactFlowFormat = useCallback(() => {
    if (!workflow) return;

    // Convert nodes
    const reactFlowNodes: Node[] = workflow.nodes.map((node) => {
      const config = nodeConfigs[node.type];
      const executionStatus = getNodeExecutionStatus(node.key);

      return {
        id: node.key,
        type: 'custom',
        position: node.position || { x: 0, y: 0 },
        data: {
          ...node,
          config,
          executionStatus,
          isSelected: selectedNodes.includes(node.key),
        },
        selected: selectedNodes.includes(node.key),
      };
    });

    // Convert connections to edges
    const reactFlowEdges: Edge[] = workflow.connections.map((conn) => ({
      id: conn.id,
      source: conn.from_node,
      target: conn.to_node,
      sourceHandle: conn.from_port,
      targetHandle: conn.to_port,
      type: 'smoothstep',
      animated: execution?.status === 'running',
    }));

    setNodes(reactFlowNodes);
    setEdges(reactFlowEdges);
  }, [workflow, selectedNodes, execution, getNodeExecutionStatus, setNodes, setEdges]);

  // Update ReactFlow when workflow changes
  useEffect(() => {
    convertToReactFlowFormat();
  }, [convertToReactFlowFormat]);

  // Handle node drag
  const onNodeDragStop = useCallback(
    (event: React.MouseEvent, node: Node) => {
      if (readOnly) return;
      updateNode(node.id, { position: node.position });
    },
    [updateNode, readOnly]
  );

  // Handle connection creation
  const onConnect = useCallback(
    (params: Connection) => {
      if (readOnly) return;

      const connection: WorkflowConnection = {
        id: `${params.source}-${params.target}-${Date.now()}`,
        from_node: params.source!,
        to_node: params.target!,
        from_port: params.sourceHandle || undefined,
        to_port: params.targetHandle || undefined,
      };

      addConnection(connection);
    },
    [addConnection, readOnly]
  );

  // Handle edge deletion
  const onEdgesDelete = useCallback(
    (edgesToDelete: Edge[]) => {
      if (readOnly) return;
      edgesToDelete.forEach((edge) => {
        removeConnection(edge.id);
      });
    },
    [removeConnection, readOnly]
  );

  // Handle node selection
  const onNodeClick = useCallback(
    (event: React.MouseEvent, node: Node) => {
      event.stopPropagation();
      selectNode(node.id, event.ctrlKey || event.metaKey);
    },
    [selectNode]
  );

  // Handle canvas click (clear selection)
  const onPaneClick = useCallback(() => {
    clearSelection();
  }, [clearSelection]);

  // Handle viewport change
  const onMove = useCallback(
    (event: any, viewport: { x: number; y: number; zoom: number }) => {
      setViewport({ x: viewport.x, y: viewport.y });
      setZoom(viewport.zoom);
    },
    [setViewport, setZoom]
  );

  // Toolbar handlers
  const handleExecute = useCallback(() => {
    console.log('Execute workflow');
  }, []);

  const handleStop = useCallback(() => {
    console.log('Stop workflow');
  }, []);

  const handleSave = useCallback(() => {
    console.log('Save workflow');
  }, []);

  const handleUndo = useCallback(() => {
    console.log('Undo');
  }, []);

  const handleRedo = useCallback(() => {
    console.log('Redo');
  }, []);

  const handleZoomIn = useCallback(() => {
    console.log('Zoom in');
  }, []);

  const handleZoomOut = useCallback(() => {
    console.log('Zoom out');
  }, []);

  const handleFitView = useCallback(() => {
    console.log('Fit view');
  }, []);

  const handleSettings = useCallback(() => {
    console.log('Settings');
  }, []);

  const handleShare = useCallback(() => {
    console.log('Share');
  }, []);

  // Handle node drop from palette
  const onDrop = useCallback(
    (event: React.DragEvent) => {
      if (readOnly) return;

      event.preventDefault();

      const reactFlowBounds = reactFlowWrapper.current?.getBoundingClientRect();
      if (!reactFlowBounds) return;

      const nodeType = event.dataTransfer.getData('application/reactflow');
      if (!nodeType || !Object.values(NodeType).includes(nodeType as NodeType)) return;

      const position = {
        x: event.clientX - reactFlowBounds.left,
        y: event.clientY - reactFlowBounds.top,
      };

      const newNode: NodeSchema = {
        key: `${nodeType}-${Date.now()}`,
        name: nodeConfigs[nodeType as NodeType].label,
        type: nodeType as NodeType,
        position,
        config: {},
        input_types: nodeConfigs[nodeType as NodeType].inputs,
        output_types: nodeConfigs[nodeType as NodeType].outputs,
      };

      addNode(newNode);
    },
    [addNode, readOnly]
  );

  const onDragOver = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    event.dataTransfer.dropEffect = 'move';
  }, []);

  if (!workflow) {
    return (
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        height="100%"
        bgcolor="grey.100"
      >
        <Typography variant="h6" color="text.secondary">
          Loading workflow...
        </Typography>
      </Box>
    );
  }

  return (
    <Box
      display="flex"
      flexDirection="column"
      height="100vh"
      bgcolor="#f5f6fa"
    >
      {/* Toolbar */}
      <Toolbar
        workflowName={workflow?.name || 'Untitled Workflow'}
        isExecuting={false}
        canUndo={false}
        canRedo={false}
        onExecute={handleExecute}
        onStop={handleStop}
        onSave={handleSave}
        onUndo={handleUndo}
        onRedo={handleRedo}
        onZoomIn={handleZoomIn}
        onZoomOut={handleZoomOut}
        onFitView={handleFitView}
        onSettings={handleSettings}
        onShare={handleShare}
      />

      {/* Main Content Area */}
      <Box display="flex" flex={1}>
        {/* Node Palette */}
        {!readOnly && (
        <Box
          width={280}
          sx={{
            backgroundColor: '#fafbfc',
            borderRight: '1px solid #e5e7eb',
            zIndex: 10,
            flexShrink: 0,
          }}
        >
          <NodePalette />
        </Box>
      )}

      {/* Main Editor */}
      <Box flex={1} position="relative" bgcolor="#f9fafb">
        <div
          ref={reactFlowWrapper}
          style={{ width: '100%', height: '100%' }}
          onDrop={onDrop}
          onDragOver={onDragOver}
        >
          <ReactFlow
            nodes={nodes}
            edges={edges}
            onNodesChange={onNodesChange}
            onEdgesChange={onEdgesChange}
            onConnect={onConnect}
            onEdgesDelete={onEdgesDelete}
            onNodeClick={onNodeClick}
            onNodeDragStop={onNodeDragStop}
            onPaneClick={onPaneClick}
            onMove={onMove}
            nodeTypes={nodeTypes}
            connectionMode={ConnectionMode.Loose}
            fitView
            attributionPosition="bottom-left"
          >
            <Controls
              style={{
                backgroundColor: '#ffffff',
                border: '1px solid #e1e5e9',
                borderRadius: 8,
                boxShadow: '0 4px 12px rgba(0, 0, 0, 0.08)',
              }}
            />
            <MiniMap
              nodeColor={(node) => {
                const nodeData = node.data;
                return nodeData?.config?.color || '#94a3b8';
              }}
              style={{
                backgroundColor: '#ffffff',
                border: '1px solid #e1e5e9',
                borderRadius: 8,
                boxShadow: '0 4px 12px rgba(0, 0, 0, 0.08)',
              }}
            />
            <Background
              color="#e5e7eb"
              gap={16}
              size={0.5}
            />
            
            {/* Execution Status Panel */}
            {execution && (
              <Panel position="top-right">
                <Paper sx={{ p: 2, minWidth: 200 }}>
                  <ExecutionPanel />
                </Paper>
              </Panel>
            )}
          </ReactFlow>
        </div>
      </Box>

        {/* Properties Panel */}
        {!readOnly && selectedNodes.length > 0 && (
          <Box width={320} bgcolor="white" borderLeft={1} borderColor="grey.300">
            <NodePropertiesPanel />
          </Box>
        )}
      </Box>
    </Box>
  );
};

export default WorkflowEditor;
