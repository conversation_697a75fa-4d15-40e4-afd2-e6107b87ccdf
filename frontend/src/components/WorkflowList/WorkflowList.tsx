import React from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  CardActions,
  Grid,
  Chip,
  IconButton,
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import EditIcon from '@mui/icons-material/Edit';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import DeleteIcon from '@mui/icons-material/Delete';
import { useNavigate } from 'react-router-dom';

const WorkflowList: React.FC = () => {
  const navigate = useNavigate();

  // Mock data for now
  const workflows = [
    {
      id: '1',
      name: 'Sample Workflow',
      description: 'A sample workflow for demonstration',
      status: 'draft',
      created_at: '2023-12-01T10:00:00Z',
      updated_at: '2023-12-01T10:00:00Z',
    },
  ];

  const handleCreateWorkflow = () => {
    // Create a new workflow and navigate to editor
    const newWorkflowId = `workflow_${Date.now()}`;
    navigate(`/workflows/${newWorkflowId}/edit`);
  };

  const handleEditWorkflow = (id: string) => {
    navigate(`/workflows/${id}/edit`);
  };

  const handleRunWorkflow = (id: string) => {
    // TODO: Implement run workflow
    console.log('Run workflow', id);
  };

  const handleDeleteWorkflow = (id: string) => {
    // TODO: Implement delete workflow
    console.log('Delete workflow', id);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'published':
        return 'success';
      case 'draft':
        return 'warning';
      case 'archived':
        return 'default';
      default:
        return 'default';
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" fontWeight={600}>
          Workflows
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={handleCreateWorkflow}
        >
          Create Workflow
        </Button>
      </Box>

      {workflows.length === 0 ? (
        <Box
          display="flex"
          flexDirection="column"
          alignItems="center"
          justifyContent="center"
          minHeight={400}
          textAlign="center"
        >
          <Typography variant="h6" color="text.secondary" gutterBottom>
            No workflows found
          </Typography>
          <Typography variant="body2" color="text.secondary" mb={3}>
            Create your first workflow to get started
          </Typography>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleCreateWorkflow}
          >
            Create Workflow
          </Button>
        </Box>
      ) : (
        <Grid container spacing={3}>
          {workflows.map((workflow) => (
            <Grid item xs={12} sm={6} md={4} key={workflow.id}>
              <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                <CardContent sx={{ flexGrow: 1 }}>
                  <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={2}>
                    <Typography variant="h6" component="h2" gutterBottom>
                      {workflow.name}
                    </Typography>
                    <Chip
                      label={workflow.status}
                      color={getStatusColor(workflow.status)}
                      size="small"
                    />
                  </Box>
                  
                  <Typography variant="body2" color="text.secondary" mb={2}>
                    {workflow.description}
                  </Typography>
                  
                  <Typography variant="caption" color="text.secondary" display="block">
                    Created: {new Date(workflow.created_at).toLocaleDateString()}
                  </Typography>
                  <Typography variant="caption" color="text.secondary" display="block">
                    Updated: {new Date(workflow.updated_at).toLocaleDateString()}
                  </Typography>
                </CardContent>
                
                <CardActions sx={{ justifyContent: 'space-between', px: 2, pb: 2 }}>
                  <Box>
                    <IconButton
                      size="small"
                      onClick={() => handleEditWorkflow(workflow.id)}
                      color="primary"
                    >
                      <EditIcon />
                    </IconButton>
                    <IconButton
                      size="small"
                      onClick={() => handleRunWorkflow(workflow.id)}
                      color="success"
                    >
                      <PlayArrowIcon />
                    </IconButton>
                  </Box>
                  <IconButton
                    size="small"
                    onClick={() => handleDeleteWorkflow(workflow.id)}
                    color="error"
                  >
                    <DeleteIcon />
                  </IconButton>
                </CardActions>
              </Card>
            </Grid>
          ))}
        </Grid>
      )}
    </Box>
  );
};

export default WorkflowList;
