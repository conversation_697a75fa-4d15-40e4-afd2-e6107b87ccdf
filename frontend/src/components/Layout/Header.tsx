import React from 'react';
import {
  AppB<PERSON>,
  Toolbar,
  Typography,
  Button,
  Box,
  IconButton,
} from '@mui/material';
import { useNavigate, useLocation } from 'react-router-dom';
import HomeIcon from '@mui/icons-material/Home';
import AccountTreeIcon from '@mui/icons-material/AccountTree';

const Header: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();

  const handleHomeClick = () => {
    navigate('/workflows');
  };

  const isWorkflowsPage = location.pathname === '/workflows';

  return (
    <AppBar position="static" elevation={1}>
      <Toolbar>
        <Box display="flex" alignItems="center" sx={{ flexGrow: 1 }}>
          <AccountTreeIcon sx={{ mr: 2 }} />
          <Typography variant="h6" component="div" fontWeight={600}>
            Workflow Engine
          </Typography>
        </Box>

        <Box display="flex" alignItems="center" gap={1}>
          {!isWorkflowsPage && (
            <Button
              color="inherit"
              startIcon={<HomeIcon />}
              onClick={handleHomeClick}
            >
              Workflows
            </Button>
          )}
          
          <Typography variant="body2" color="inherit" sx={{ opacity: 0.7, ml: 2 }}>
            v1.0.0
          </Typography>
        </Box>
      </Toolbar>
    </AppBar>
  );
};

export default Header;
