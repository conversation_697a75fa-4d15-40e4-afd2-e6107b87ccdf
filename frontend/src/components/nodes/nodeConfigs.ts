import { NodeType, NodeUIData, TypeInfo } from '../../types/workflow';

// Common input/output types
const commonTypes = {
  string: { type: 'string', description: 'Text string' },
  number: { type: 'number', description: 'Numeric value' },
  boolean: { type: 'boolean', description: 'True or false value' },
  object: { type: 'object', description: 'JSON object' },
  array: { type: 'array', description: 'Array of values' },
  any: { type: 'any', description: 'Any type of value' },
  message: { type: 'message', description: 'Chat message' },
  file: { type: 'file', description: 'File reference' },
  image: { type: 'image', description: 'Image data' },
  audio: { type: 'audio', description: 'Audio data' },
  video: { type: 'video', description: 'Video data' },
};

// Node configurations
export const nodeConfigs: Record<NodeType, NodeUIData> = {
  [NodeType.ENTRY]: {
    label: 'Entry',
    color: '#4CAF50',
    icon: '🚀',
    category: 'Control',
    description: 'Workflow entry point',
    inputs: {},
    outputs: {
      output: commonTypes.any,
    },
    configSchema: {
      type: 'object',
      properties: {
        description: {
          type: 'string',
          title: 'Description',
          description: 'Optional description for this entry point',
        },
      },
    },
  },

  [NodeType.EXIT]: {
    label: 'Exit',
    color: '#F44336',
    icon: '🏁',
    category: 'Control',
    description: 'Workflow exit point',
    inputs: {
      input: commonTypes.any,
    },
    outputs: {},
    configSchema: {
      type: 'object',
      properties: {
        description: {
          type: 'string',
          title: 'Description',
          description: 'Optional description for this exit point',
        },
      },
    },
  },

  [NodeType.SELECTOR]: {
    label: 'Selector',
    color: '#FF9800',
    icon: '🔀',
    category: 'Logic',
    description: 'Conditional branching based on input conditions',
    inputs: {
      input: commonTypes.any,
    },
    outputs: {
      branch: commonTypes.number,
      matched: commonTypes.boolean,
      condition: commonTypes.object,
    },
    configSchema: {
      type: 'object',
      properties: {
        conditions: {
          type: 'array',
          title: 'Conditions',
          description: 'List of conditions to evaluate',
          items: {
            type: 'object',
            properties: {
              field: {
                type: 'string',
                title: 'Field',
                description: 'Field name to check',
              },
              operator: {
                type: 'string',
                title: 'Operator',
                enum: ['equals', 'not_equals', 'contains', 'greater_than', 'less_than', 'is_empty', 'is_not_empty'],
                description: 'Comparison operator',
              },
              value: {
                title: 'Value',
                description: 'Value to compare against',
              },
            },
            required: ['field', 'operator'],
          },
        },
      },
      required: ['conditions'],
    },
  },

  [NodeType.VARIABLE_ASSIGNER]: {
    label: 'Variable Assigner',
    color: '#9C27B0',
    icon: '📝',
    category: 'Data',
    description: 'Assign values to variables',
    inputs: {
      input: commonTypes.any,
    },
    outputs: {
      output: commonTypes.any,
    },
    configSchema: {
      type: 'object',
      properties: {
        assignments: {
          type: 'array',
          title: 'Assignments',
          description: 'List of variable assignments',
          items: {
            type: 'object',
            properties: {
              variable: {
                type: 'string',
                title: 'Variable Name',
                description: 'Name of the variable to assign',
              },
              type: {
                type: 'string',
                title: 'Assignment Type',
                enum: ['constant', 'reference', 'expression'],
                description: 'Type of assignment',
              },
              value: {
                title: 'Value',
                description: 'Value to assign (for constant type)',
              },
              source: {
                type: 'string',
                title: 'Source Field',
                description: 'Source field name (for reference type)',
              },
              expression: {
                type: 'string',
                title: 'Expression',
                description: 'Expression to evaluate (for expression type)',
              },
            },
            required: ['variable', 'type'],
          },
        },
      },
      required: ['assignments'],
    },
  },

  [NodeType.TEXT_PROCESSOR]: {
    label: 'Text Processor',
    color: '#2196F3',
    icon: '📄',
    category: 'Data',
    description: 'Process and manipulate text',
    inputs: {
      text: commonTypes.string,
    },
    outputs: {
      result: commonTypes.any,
    },
    configSchema: {
      type: 'object',
      properties: {
        operation: {
          type: 'string',
          title: 'Operation',
          enum: ['uppercase', 'lowercase', 'trim', 'length', 'split', 'replace'],
          description: 'Text processing operation',
        },
        input_field: {
          type: 'string',
          title: 'Input Field',
          default: 'text',
          description: 'Name of the input field containing text',
        },
        output_field: {
          type: 'string',
          title: 'Output Field',
          default: 'result',
          description: 'Name of the output field for result',
        },
        delimiter: {
          type: 'string',
          title: 'Delimiter',
          description: 'Delimiter for split operation',
        },
        old: {
          type: 'string',
          title: 'Old String',
          description: 'String to replace (for replace operation)',
        },
        new: {
          type: 'string',
          title: 'New String',
          description: 'Replacement string (for replace operation)',
        },
      },
      required: ['operation'],
    },
  },

  [NodeType.LLM]: {
    label: 'LLM',
    color: '#673AB7',
    icon: '🤖',
    category: 'AI',
    description: 'Large Language Model for text generation and conversation',
    inputs: {
      messages: { type: 'array', description: 'Chat messages array' },
      user_input: commonTypes.string,
      system_prompt: commonTypes.string,
      context: commonTypes.string,
      variables: commonTypes.object,
    },
    outputs: {
      response: commonTypes.string,
      messages: { type: 'array', description: 'Updated messages array' },
      tokens_used: commonTypes.number,
      finish_reason: commonTypes.string,
      model_used: commonTypes.string,
    },
    configSchema: {
      type: 'object',
      properties: {
        model: {
          type: 'string',
          title: 'Model',
          description: 'LLM model to use',
          enum: [
            'gpt-4o',
            'gpt-4o-mini',
            'gpt-4-turbo',
            'gpt-3.5-turbo',
            'claude-3-5-sonnet',
            'claude-3-opus',
            'claude-3-haiku',
            'gemini-1.5-pro',
            'gemini-1.5-flash'
          ],
          default: 'gpt-4o-mini',
        },
        system_prompt: {
          type: 'string',
          title: 'System Prompt',
          description: 'System message to guide the model behavior',
          format: 'textarea',
        },
        user_prompt: {
          type: 'string',
          title: 'User Prompt',
          description: 'Template for user messages (supports variables like {{input}})',
          format: 'textarea',
          default: '{{user_input}}',
        },
        temperature: {
          type: 'number',
          title: 'Temperature',
          description: 'Controls randomness (0-2)',
          minimum: 0,
          maximum: 2,
          default: 0.7,
        },
        max_tokens: {
          type: 'number',
          title: 'Max Tokens',
          description: 'Maximum tokens to generate',
          minimum: 1,
          maximum: 8192,
          default: 1000,
        },
        top_p: {
          type: 'number',
          title: 'Top P',
          description: 'Nucleus sampling parameter',
          minimum: 0,
          maximum: 1,
          default: 1,
        },
        output_format: {
          type: 'string',
          title: 'Output Format',
          description: 'Expected output format',
          enum: ['text', 'json', 'markdown'],
          default: 'text',
        },
        stream: {
          type: 'boolean',
          title: 'Stream Response',
          description: 'Enable streaming response',
          default: false,
        },
      },
      required: ['model'],
    },
  },

  [NodeType.HTTP_REQUESTER]: {
    label: 'HTTP Requester',
    color: '#FF5722',
    icon: '🌐',
    category: 'Integration',
    description: 'Make HTTP requests to external APIs',
    inputs: {
      url: commonTypes.string,
      headers: commonTypes.object,
      body: commonTypes.any,
    },
    outputs: {
      http_response: commonTypes.object,
    },
    configSchema: {
      type: 'object',
      properties: {
        url: {
          type: 'string',
          title: 'URL',
          description: 'HTTP request URL',
          format: 'uri',
        },
        method: {
          type: 'string',
          title: 'Method',
          enum: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
          default: 'GET',
          description: 'HTTP method',
        },
        headers: {
          type: 'object',
          title: 'Headers',
          description: 'HTTP headers as key-value pairs',
        },
        timeout: {
          type: 'number',
          title: 'Timeout (seconds)',
          minimum: 1,
          default: 30,
          description: 'Request timeout in seconds',
        },
      },
      required: ['url'],
    },
  },

  [NodeType.CODE_RUNNER]: {
    label: 'Code Runner',
    color: '#795548',
    icon: '💻',
    category: 'Compute',
    description: 'Execute custom code in various programming languages',
    inputs: {
      input: commonTypes.any,
      params: commonTypes.object,
    },
    outputs: {
      result: commonTypes.any,
      output: commonTypes.string,
      error: commonTypes.string,
      execution_time: commonTypes.number,
    },
    configSchema: {
      type: 'object',
      properties: {
        language: {
          type: 'string',
          title: 'Language',
          enum: ['javascript', 'python', 'bash', 'typescript'],
          default: 'javascript',
          description: 'Programming language to execute',
        },
        code: {
          type: 'string',
          title: 'Code',
          description: 'Code to execute. Use "input" variable to access node input.',
          format: 'code',
          default: '// Access input data\nconst data = input;\n\n// Your code here\nreturn {\n  message: "Hello from code!",\n  processed: data\n};',
        },
        timeout: {
          type: 'number',
          title: 'Timeout (seconds)',
          minimum: 1,
          maximum: 300,
          default: 30,
          description: 'Maximum execution time',
        },
        memory_limit: {
          type: 'number',
          title: 'Memory Limit (MB)',
          minimum: 16,
          maximum: 512,
          default: 128,
          description: 'Maximum memory usage',
        },
        packages: {
          type: 'array',
          title: 'Packages',
          description: 'Additional packages to import (Python/Node.js)',
          items: {
            type: 'string',
          },
        },
        environment: {
          type: 'object',
          title: 'Environment Variables',
          description: 'Environment variables for code execution',
          additionalProperties: {
            type: 'string',
          },
        },
      },
      required: ['code', 'language'],
    },
  },

  [NodeType.CONTINUE]: {
    label: 'Continue',
    color: '#607D8B',
    icon: '➡️',
    category: 'Control',
    description: 'Continue execution (no-op)',
    inputs: {
      input: commonTypes.any,
    },
    outputs: {
      output: commonTypes.any,
    },
    configSchema: {
      type: 'object',
      properties: {
        description: {
          type: 'string',
          title: 'Description',
          description: 'Optional description',
        },
      },
    },
  },

  [NodeType.BREAK]: {
    label: 'Break',
    color: '#E91E63',
    icon: '🛑',
    category: 'Control',
    description: 'Break execution',
    inputs: {
      input: commonTypes.any,
    },
    outputs: {
      break: commonTypes.boolean,
    },
    configSchema: {
      type: 'object',
      properties: {
        description: {
          type: 'string',
          title: 'Description',
          description: 'Optional description',
        },
      },
    },
  },

  [NodeType.LOOP]: {
    label: 'Loop',
    color: '#3F51B5',
    icon: '🔄',
    category: 'Control',
    description: 'Loop iteration control',
    inputs: {
      items: commonTypes.array,
      condition: commonTypes.boolean,
    },
    outputs: {
      item: commonTypes.any,
      index: commonTypes.number,
      continue: commonTypes.boolean,
    },
    configSchema: {
      type: 'object',
      properties: {
        type: {
          type: 'string',
          title: 'Loop Type',
          enum: ['for_each', 'while', 'for'],
          default: 'for_each',
          description: 'Type of loop',
        },
        max_iterations: {
          type: 'number',
          title: 'Max Iterations',
          minimum: 1,
          default: 100,
          description: 'Maximum number of iterations',
        },
      },
      required: ['type'],
    },
  },

  [NodeType.BATCH]: {
    label: 'Batch',
    color: '#009688',
    icon: '📦',
    category: 'Control',
    description: 'Batch processing of multiple items',
    inputs: {
      items: commonTypes.array,
    },
    outputs: {
      results: commonTypes.array,
      success_count: commonTypes.number,
      error_count: commonTypes.number,
    },
    configSchema: {
      type: 'object',
      properties: {
        batch_size: {
          type: 'number',
          title: 'Batch Size',
          minimum: 1,
          default: 10,
          description: 'Number of items to process in each batch',
        },
        parallel: {
          type: 'boolean',
          title: 'Parallel Processing',
          default: false,
          description: 'Process items in parallel',
        },
        continue_on_error: {
          type: 'boolean',
          title: 'Continue on Error',
          default: true,
          description: 'Continue processing if an item fails',
        },
      },
    },
  },

  // Database Nodes
  [NodeType.DATABASE_QUERY]: {
    label: 'Database Query',
    color: '#4CAF50',
    icon: '🔍',
    category: 'Database',
    description: 'Query data from database',
    inputs: {
      query: commonTypes.string,
      parameters: commonTypes.object,
    },
    outputs: {
      results: commonTypes.array,
      count: commonTypes.number,
    },
    configSchema: {
      type: 'object',
      properties: {
        connection: {
          type: 'string',
          title: 'Database Connection',
          description: 'Database connection string or reference',
        },
        query: {
          type: 'string',
          title: 'SQL Query',
          description: 'SQL query to execute (supports parameters)',
          format: 'textarea',
        },
        timeout: {
          type: 'number',
          title: 'Timeout (seconds)',
          default: 30,
          minimum: 1,
          maximum: 300,
        },
      },
      required: ['connection', 'query'],
    },
  },

  [NodeType.DATABASE_CREATE]: {
    label: 'Database Create',
    color: '#2196F3',
    icon: '➕',
    category: 'Database',
    description: 'Insert new records into database',
    inputs: {
      data: commonTypes.object,
      table: commonTypes.string,
    },
    outputs: {
      inserted_id: commonTypes.any,
      affected_rows: commonTypes.number,
    },
    configSchema: {
      type: 'object',
      properties: {
        connection: {
          type: 'string',
          title: 'Database Connection',
          description: 'Database connection string or reference',
        },
        table: {
          type: 'string',
          title: 'Table Name',
          description: 'Target table for insertion',
        },
        fields: {
          type: 'object',
          title: 'Field Mapping',
          description: 'Map input fields to database columns',
          additionalProperties: {
            type: 'string',
          },
        },
      },
      required: ['connection', 'table'],
    },
  },

  [NodeType.DATABASE_UPDATE]: {
    label: 'Database Update',
    color: '#FF9800',
    icon: '✏️',
    category: 'Database',
    description: 'Update existing records in database',
    inputs: {
      data: commonTypes.object,
      conditions: commonTypes.object,
    },
    outputs: {
      affected_rows: commonTypes.number,
      success: commonTypes.boolean,
    },
    configSchema: {
      type: 'object',
      properties: {
        connection: {
          type: 'string',
          title: 'Database Connection',
          description: 'Database connection string or reference',
        },
        table: {
          type: 'string',
          title: 'Table Name',
          description: 'Target table for update',
        },
        where_clause: {
          type: 'string',
          title: 'WHERE Clause',
          description: 'SQL WHERE clause for update conditions',
        },
      },
      required: ['connection', 'table', 'where_clause'],
    },
  },

  [NodeType.DATABASE_DELETE]: {
    label: 'Database Delete',
    color: '#F44336',
    icon: '🗑️',
    category: 'Database',
    description: 'Delete records from database',
    inputs: {
      conditions: commonTypes.object,
    },
    outputs: {
      deleted_rows: commonTypes.number,
      success: commonTypes.boolean,
    },
    configSchema: {
      type: 'object',
      properties: {
        connection: {
          type: 'string',
          title: 'Database Connection',
          description: 'Database connection string or reference',
        },
        table: {
          type: 'string',
          title: 'Table Name',
          description: 'Target table for deletion',
        },
        where_clause: {
          type: 'string',
          title: 'WHERE Clause',
          description: 'SQL WHERE clause for delete conditions',
        },
        confirm_delete: {
          type: 'boolean',
          title: 'Confirm Delete',
          description: 'Require confirmation for delete operations',
          default: true,
        },
      },
      required: ['connection', 'table', 'where_clause'],
    },
  },

  // Knowledge & Data Nodes
  [NodeType.KNOWLEDGE_RETRIEVER]: {
    label: 'Knowledge Retriever',
    color: '#8BC34A',
    icon: '📚',
    category: 'Knowledge',
    description: 'Retrieve information from knowledge base',
    inputs: {
      query: commonTypes.string,
      filters: commonTypes.object,
    },
    outputs: {
      results: commonTypes.array,
      relevance_scores: commonTypes.array,
      total_count: commonTypes.number,
    },
    configSchema: {
      type: 'object',
      properties: {
        knowledge_base: {
          type: 'string',
          title: 'Knowledge Base',
          description: 'Knowledge base identifier',
        },
        max_results: {
          type: 'number',
          title: 'Max Results',
          default: 10,
          minimum: 1,
          maximum: 100,
          description: 'Maximum number of results to return',
        },
        min_relevance: {
          type: 'number',
          title: 'Min Relevance Score',
          default: 0.7,
          minimum: 0,
          maximum: 1,
          description: 'Minimum relevance score threshold',
        },
        include_metadata: {
          type: 'boolean',
          title: 'Include Metadata',
          default: true,
          description: 'Include document metadata in results',
        },
      },
      required: ['knowledge_base'],
    },
  },

  [NodeType.KNOWLEDGE_WRITER]: {
    label: 'Knowledge Writer',
    color: '#CDDC39',
    icon: '📝',
    category: 'Knowledge',
    description: 'Write information to knowledge base',
    inputs: {
      content: commonTypes.string,
      metadata: commonTypes.object,
    },
    outputs: {
      document_id: commonTypes.string,
      success: commonTypes.boolean,
    },
    configSchema: {
      type: 'object',
      properties: {
        knowledge_base: {
          type: 'string',
          title: 'Knowledge Base',
          description: 'Knowledge base identifier',
        },
        chunk_size: {
          type: 'number',
          title: 'Chunk Size',
          default: 1000,
          minimum: 100,
          maximum: 4000,
          description: 'Text chunk size for processing',
        },
        overlap: {
          type: 'number',
          title: 'Overlap',
          default: 100,
          minimum: 0,
          maximum: 500,
          description: 'Overlap between chunks',
        },
      },
      required: ['knowledge_base'],
    },
  },

  // Input/Output Nodes
  [NodeType.INPUT_RECEIVER]: {
    label: 'Input Receiver',
    color: '#00BCD4',
    icon: '📥',
    category: 'Input/Output',
    description: 'Receive input from user or external source',
    inputs: {},
    outputs: {
      user_input: commonTypes.any,
      timestamp: commonTypes.string,
    },
    configSchema: {
      type: 'object',
      properties: {
        input_type: {
          type: 'string',
          title: 'Input Type',
          enum: ['text', 'file', 'image', 'audio', 'video', 'json'],
          default: 'text',
          description: 'Expected input type',
        },
        prompt: {
          type: 'string',
          title: 'Prompt',
          description: 'Prompt message for user input',
        },
        required: {
          type: 'boolean',
          title: 'Required',
          default: true,
          description: 'Whether input is required',
        },
        validation: {
          type: 'object',
          title: 'Validation Rules',
          description: 'Input validation rules',
          properties: {
            min_length: { type: 'number' },
            max_length: { type: 'number' },
            pattern: { type: 'string' },
          },
        },
      },
    },
  },

  [NodeType.OUTPUT_EMITTER]: {
    label: 'Output Emitter',
    color: '#FF6F00',
    icon: '📤',
    category: 'Input/Output',
    description: 'Emit output to user or external destination',
    inputs: {
      content: commonTypes.any,
      format: commonTypes.string,
    },
    outputs: {
      success: commonTypes.boolean,
      message_id: commonTypes.string,
    },
    configSchema: {
      type: 'object',
      properties: {
        output_type: {
          type: 'string',
          title: 'Output Type',
          enum: ['text', 'file', 'image', 'audio', 'video', 'json', 'html'],
          default: 'text',
          description: 'Output format type',
        },
        template: {
          type: 'string',
          title: 'Template',
          description: 'Output template (supports variables)',
          format: 'textarea',
        },
        destination: {
          type: 'string',
          title: 'Destination',
          enum: ['user', 'file', 'webhook', 'email'],
          default: 'user',
          description: 'Output destination',
        },
      },
    },
  },

  [NodeType.QUESTION_ANSWER]: {
    label: 'Question Answer',
    color: '#E1BEE7',
    icon: '❓',
    category: 'Input/Output',
    description: 'Interactive question and answer',
    inputs: {
      question: commonTypes.string,
      context: commonTypes.string,
    },
    outputs: {
      answer: commonTypes.string,
      confidence: commonTypes.number,
    },
    configSchema: {
      type: 'object',
      properties: {
        question_type: {
          type: 'string',
          title: 'Question Type',
          enum: ['open', 'multiple_choice', 'yes_no', 'rating'],
          default: 'open',
          description: 'Type of question',
        },
        options: {
          type: 'array',
          title: 'Options',
          description: 'Options for multiple choice questions',
          items: {
            type: 'string',
          },
        },
        timeout: {
          type: 'number',
          title: 'Timeout (seconds)',
          default: 60,
          minimum: 10,
          maximum: 300,
          description: 'Response timeout',
        },
      },
    },
  },

  // Utility Nodes
  [NodeType.INTENT_DETECTOR]: {
    label: 'Intent Detector',
    color: '#9E9E9E',
    icon: '🎯',
    category: 'Utilities',
    description: 'Detect user intent from input',
    inputs: {
      text: commonTypes.string,
      context: commonTypes.object,
    },
    outputs: {
      intent: commonTypes.string,
      confidence: commonTypes.number,
      entities: commonTypes.array,
    },
    configSchema: {
      type: 'object',
      properties: {
        model: {
          type: 'string',
          title: 'Intent Model',
          description: 'Intent detection model to use',
          default: 'default',
        },
        intents: {
          type: 'array',
          title: 'Expected Intents',
          description: 'List of possible intents',
          items: {
            type: 'object',
            properties: {
              name: { type: 'string' },
              description: { type: 'string' },
              examples: {
                type: 'array',
                items: { type: 'string' },
              },
            },
          },
        },
        confidence_threshold: {
          type: 'number',
          title: 'Confidence Threshold',
          default: 0.8,
          minimum: 0,
          maximum: 1,
          description: 'Minimum confidence for intent detection',
        },
      },
    },
  },

  [NodeType.VARIABLE_AGGREGATOR]: {
    label: 'Variable Aggregator',
    color: '#607D8B',
    icon: '🔗',
    category: 'Data',
    description: 'Aggregate and combine variables',
    inputs: {
      variables: commonTypes.object,
    },
    outputs: {
      aggregated: commonTypes.object,
    },
    configSchema: {
      type: 'object',
      properties: {
        operations: {
          type: 'array',
          title: 'Aggregation Operations',
          description: 'List of aggregation operations',
          items: {
            type: 'object',
            properties: {
              operation: {
                type: 'string',
                enum: ['sum', 'average', 'min', 'max', 'count', 'concat', 'merge'],
                description: 'Aggregation operation',
              },
              field: {
                type: 'string',
                description: 'Field to aggregate',
              },
              output_field: {
                type: 'string',
                description: 'Output field name',
              },
            },
          },
        },
      },
    },
  },

  [NodeType.SUB_WORKFLOW]: {
    label: 'Sub Workflow',
    color: '#3F51B5',
    icon: '🔄',
    category: 'Integration',
    description: 'Execute another workflow as a sub-process',
    inputs: {
      input: commonTypes.any,
    },
    outputs: {
      output: commonTypes.any,
      execution_id: commonTypes.string,
    },
    configSchema: {
      type: 'object',
      properties: {
        workflow_id: {
          type: 'string',
          title: 'Workflow ID',
          description: 'ID of the workflow to execute',
        },
        input_mapping: {
          type: 'object',
          title: 'Input Mapping',
          description: 'Map current variables to sub-workflow inputs',
          additionalProperties: {
            type: 'string',
          },
        },
        output_mapping: {
          type: 'object',
          title: 'Output Mapping',
          description: 'Map sub-workflow outputs to current variables',
          additionalProperties: {
            type: 'string',
          },
        },
        timeout: {
          type: 'number',
          title: 'Timeout (seconds)',
          default: 300,
          minimum: 10,
          maximum: 3600,
          description: 'Sub-workflow execution timeout',
        },
      },
      required: ['workflow_id'],
    },
  },

  [NodeType.COMMENT]: {
    label: 'Comment',
    color: '#FFC107',
    icon: '💬',
    category: 'Utilities',
    description: 'Add comments and documentation',
    inputs: {},
    outputs: {},
    configSchema: {
      type: 'object',
      properties: {
        comment: {
          type: 'string',
          title: 'Comment',
          description: 'Comment text',
          format: 'textarea',
        },
        color: {
          type: 'string',
          title: 'Color',
          enum: ['yellow', 'blue', 'green', 'red', 'purple'],
          default: 'yellow',
          description: 'Comment color',
        },
      },
    },
  },

  // New utility nodes
  [NodeType.EMAIL_SENDER]: {
    label: 'Email Sender',
    color: '#1976D2',
    icon: '📧',
    category: 'Communication',
    description: 'Send email notifications',
    inputs: {
      to: commonTypes.string,
      subject: commonTypes.string,
      body: commonTypes.string,
      data: commonTypes.object,
    },
    outputs: {
      success: commonTypes.boolean,
      message_id: commonTypes.string,
      sent_at: commonTypes.string,
    },
    configSchema: {
      type: 'object',
      properties: {
        to: {
          type: 'string',
          title: 'To Email',
          description: 'Recipient email address',
        },
        subject: {
          type: 'string',
          title: 'Subject',
          description: 'Email subject line',
        },
        body: {
          type: 'string',
          title: 'Body',
          description: 'Email body template',
          format: 'textarea',
        },
      },
      required: ['to'],
    },
  },

  [NodeType.FILE_PROCESSOR]: {
    label: 'File Processor',
    color: '#795548',
    icon: '📁',
    category: 'File Operations',
    description: 'Process files (read/write/delete)',
    inputs: {
      file_path: commonTypes.string,
      file_content: commonTypes.string,
      operation: commonTypes.string,
    },
    outputs: {
      content: commonTypes.string,
      success: commonTypes.boolean,
      size: commonTypes.number,
    },
    configSchema: {
      type: 'object',
      properties: {
        operation: {
          type: 'string',
          title: 'Operation',
          enum: ['read', 'write', 'delete'],
          description: 'File operation to perform',
        },
      },
      required: ['operation'],
    },
  },

  [NodeType.IMAGE_PROCESSOR]: {
    label: 'Image Processor',
    color: '#E91E63',
    icon: '🖼️',
    category: 'Media',
    description: 'Process images (resize/crop/filter)',
    inputs: {
      image_url: commonTypes.string,
      image_path: commonTypes.string,
    },
    outputs: {
      output_url: commonTypes.string,
      success: commonTypes.boolean,
      operation: commonTypes.string,
    },
    configSchema: {
      type: 'object',
      properties: {
        operation: {
          type: 'string',
          title: 'Operation',
          enum: ['resize', 'crop', 'filter', 'analyze'],
          description: 'Image operation to perform',
        },
        width: {
          type: 'number',
          title: 'Width',
          description: 'Target width for resize operation',
        },
        height: {
          type: 'number',
          title: 'Height',
          description: 'Target height for resize operation',
        },
      },
      required: ['operation'],
    },
  },

  [NodeType.WEB_SCRAPER]: {
    label: 'Web Scraper',
    color: '#607D8B',
    icon: '🕷️',
    category: 'Integration',
    description: 'Scrape content from websites',
    inputs: {
      url: commonTypes.string,
    },
    outputs: {
      title: commonTypes.string,
      content: commonTypes.string,
      links: { type: 'array', description: 'Extracted links' },
      images: { type: 'array', description: 'Extracted images' },
    },
    configSchema: {
      type: 'object',
      properties: {
        url: {
          type: 'string',
          title: 'URL',
          description: 'Website URL to scrape',
        },
        selector: {
          type: 'string',
          title: 'CSS Selector',
          description: 'CSS selector for content extraction',
        },
      },
      required: ['url'],
    },
  },

  [NodeType.SCHEDULER]: {
    label: 'Scheduler',
    color: '#FF9800',
    icon: '⏰',
    category: 'Control Flow',
    description: 'Schedule task execution',
    inputs: {
      data: commonTypes.object,
    },
    outputs: {
      schedule_id: commonTypes.string,
      next_run: commonTypes.string,
      scheduled_at: commonTypes.string,
    },
    configSchema: {
      type: 'object',
      properties: {
        schedule_type: {
          type: 'string',
          title: 'Schedule Type',
          enum: ['immediate', 'delay', 'scheduled'],
          description: 'Type of scheduling',
        },
        delay_seconds: {
          type: 'number',
          title: 'Delay (seconds)',
          description: 'Delay in seconds for delay type',
        },
        schedule_time: {
          type: 'string',
          title: 'Schedule Time',
          description: 'ISO timestamp for scheduled type',
        },
      },
      required: ['schedule_type'],
    },
  },

  [NodeType.NOTIFICATION]: {
    label: 'Notification',
    color: '#FF5722',
    icon: '🔔',
    category: 'Communication',
    description: 'Send notifications',
    inputs: {
      message: commonTypes.string,
      data: commonTypes.object,
    },
    outputs: {
      notification_id: commonTypes.string,
      success: commonTypes.boolean,
      sent_at: commonTypes.string,
    },
    configSchema: {
      type: 'object',
      properties: {
        type: {
          type: 'string',
          title: 'Type',
          enum: ['info', 'warning', 'error', 'success'],
          description: 'Notification type',
        },
        message: {
          type: 'string',
          title: 'Message',
          description: 'Notification message template',
        },
        channels: {
          type: 'array',
          title: 'Channels',
          items: {
            type: 'string',
            enum: ['console', 'email', 'slack'],
          },
          description: 'Notification channels',
        },
      },
      required: ['message'],
    },
  },

  [NodeType.DATA_TRANSFORMER]: {
    label: 'Data Transformer',
    color: '#9C27B0',
    icon: '🔄',
    category: 'Data Processing',
    description: 'Transform data fields',
    inputs: {
      data: commonTypes.object,
    },
    outputs: {
      transformed_data: commonTypes.object,
    },
    configSchema: {
      type: 'object',
      properties: {
        transform_type: {
          type: 'string',
          title: 'Transform Type',
          enum: ['uppercase', 'lowercase', 'length', 'reverse'],
          description: 'Type of transformation',
        },
        source_field: {
          type: 'string',
          title: 'Source Field',
          description: 'Field to transform',
        },
        target_field: {
          type: 'string',
          title: 'Target Field',
          description: 'Target field for result',
        },
      },
      required: ['transform_type', 'source_field'],
    },
  },

  [NodeType.CONDITIONAL_ROUTER]: {
    label: 'Conditional Router',
    color: '#3F51B5',
    icon: '🛤️',
    category: 'Control Flow',
    description: 'Route based on conditions',
    inputs: {
      data: commonTypes.object,
    },
    outputs: {
      condition_met: commonTypes.boolean,
      route: commonTypes.object,
    },
    configSchema: {
      type: 'object',
      properties: {
        condition_field: {
          type: 'string',
          title: 'Condition Field',
          description: 'Field to evaluate',
        },
        operator: {
          type: 'string',
          title: 'Operator',
          enum: ['equals', 'not_equals', 'contains', 'greater_than', 'less_than'],
          description: 'Comparison operator',
        },
        condition_value: {
          type: 'string',
          title: 'Condition Value',
          description: 'Value to compare against',
        },
      },
      required: ['condition_field', 'operator', 'condition_value'],
    },
  },

  [NodeType.ERROR_HANDLER]: {
    label: 'Error Handler',
    color: '#F44336',
    icon: '⚠️',
    category: 'Control Flow',
    description: 'Handle errors with different strategies',
    inputs: {
      error: commonTypes.string,
      data: commonTypes.object,
    },
    outputs: {
      handled: commonTypes.boolean,
      strategy_used: commonTypes.string,
    },
    configSchema: {
      type: 'object',
      properties: {
        strategy: {
          type: 'string',
          title: 'Strategy',
          enum: ['log', 'retry', 'fallback', 'fail'],
          description: 'Error handling strategy',
        },
        max_retries: {
          type: 'number',
          title: 'Max Retries',
          description: 'Maximum retry attempts',
        },
        fallback_value: {
          type: 'string',
          title: 'Fallback Value',
          description: 'Fallback value for fallback strategy',
        },
      },
      required: ['strategy'],
    },
  },

  [NodeType.LOOP_CONTROLLER]: {
    label: 'Loop Controller',
    color: '#9C27B0',
    icon: '🔄',
    category: 'Control Flow',
    description: 'Control loop execution',
    inputs: {
      data: commonTypes.object,
      iteration: commonTypes.number,
    },
    outputs: {
      continue_loop: commonTypes.boolean,
      iteration_data: commonTypes.object,
    },
    configSchema: {
      type: 'object',
      properties: {
        max_iterations: {
          type: 'number',
          title: 'Max Iterations',
          description: 'Maximum number of iterations',
          default: 10,
        },
        condition_field: {
          type: 'string',
          title: 'Condition Field',
          description: 'Field to check for loop continuation',
        },
      },
      required: ['max_iterations'],
    },
  },
};

// Get node categories
export const getNodeCategories = (): string[] => {
  const categories = new Set<string>();
  Object.values(nodeConfigs).forEach((config) => {
    categories.add(config.category);
  });
  return Array.from(categories).sort();
};

// Get nodes by category
export const getNodesByCategory = (category: string): Array<{ type: NodeType; config: NodeUIData }> => {
  return Object.entries(nodeConfigs)
    .filter(([, config]) => config.category === category)
    .map(([type, config]) => ({ type: type as NodeType, config }));
};
