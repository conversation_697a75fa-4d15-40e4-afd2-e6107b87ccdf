body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

* {
  box-sizing: border-box;
}

html, body, #root {
  height: 100%;
  overflow: hidden;
}

/* ReactFlow custom styles */
.react-flow__node-custom {
  background: white;
  border: 2px solid #ddd;
  border-radius: 8px;
  padding: 10px;
  min-width: 120px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.react-flow__node-custom:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.react-flow__node-custom.selected {
  border-color: #1976d2;
  box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2);
}

.react-flow__node-custom .node-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-weight: 600;
  font-size: 14px;
}

.react-flow__node-custom .node-status {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-left: auto;
}

.react-flow__node-custom .node-status.running {
  background-color: #ff9800;
  animation: pulse 1.5s infinite;
}

.react-flow__node-custom .node-status.success {
  background-color: #4caf50;
}

.react-flow__node-custom .node-status.failed {
  background-color: #f44336;
}

.react-flow__node-custom .node-status.skipped {
  background-color: #9e9e9e;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

/* Handle styles */
.react-flow__handle {
  width: 8px;
  height: 8px;
  background: #1976d2;
  border: 2px solid white;
}

.react-flow__handle-top {
  top: -4px;
}

.react-flow__handle-bottom {
  bottom: -4px;
}

.react-flow__handle-left {
  left: -4px;
}

.react-flow__handle-right {
  right: -4px;
}

/* Edge styles */
.react-flow__edge-path {
  stroke: #b1b1b7;
  stroke-width: 2;
}

.react-flow__edge.selected .react-flow__edge-path {
  stroke: #1976d2;
}

.react-flow__edge.animated .react-flow__edge-path {
  stroke-dasharray: 5;
  animation: dashdraw 0.5s linear infinite;
}

@keyframes dashdraw {
  to {
    stroke-dashoffset: -10;
  }
}

/* Minimap styles */
.react-flow__minimap {
  background-color: #f8f9fa;
}

.react-flow__minimap-mask {
  fill: rgba(25, 118, 210, 0.1);
  stroke: #1976d2;
  stroke-width: 2;
}

/* Controls styles */
.react-flow__controls {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.react-flow__controls-button {
  background-color: white;
  border: 1px solid #ddd;
  color: #666;
}

.react-flow__controls-button:hover {
  background-color: #f5f5f5;
}

/* Panel styles */
.react-flow__panel {
  margin: 10px;
}

/* Scrollbar styles */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
