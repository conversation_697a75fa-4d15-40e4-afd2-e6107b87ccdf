import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import {
  WorkflowSchema,
  NodeSchema,
  Connection,
  NodeKey,
  WorkflowEditorState,
  Position,
} from '../types/workflow';

interface WorkflowStore extends WorkflowEditorState {
  // Actions
  setWorkflow: (workflow: WorkflowSchema | null) => void;
  updateWorkflow: (updates: Partial<WorkflowSchema>) => void;
  
  // Node operations
  addNode: (node: NodeSchema) => void;
  updateNode: (nodeKey: NodeKey, updates: Partial<NodeSchema>) => void;
  removeNode: (nodeKey: NodeKey) => void;
  moveNode: (nodeKey: NodeKey, position: Position) => void;
  
  // Connection operations
  addConnection: (connection: Connection) => void;
  removeConnection: (connectionId: string) => void;
  
  // Selection operations
  selectNode: (nodeKey: NodeKey, multiSelect?: boolean) => void;
  selectConnection: (connectionId: string, multiSelect?: boolean) => void;
  clearSelection: () => void;
  
  // Editor state
  setEditing: (isEditing: boolean) => void;
  setDirty: (isDirty: boolean) => void;
  setZoom: (zoom: number) => void;
  setViewport: (viewport: { x: number; y: number }) => void;
  
  // Utility functions
  getNode: (nodeKey: NodeKey) => NodeSchema | undefined;
  getConnection: (connectionId: string) => Connection | undefined;
  getConnectedNodes: (nodeKey: NodeKey) => { incoming: NodeKey[]; outgoing: NodeKey[] };
  validateWorkflow: () => { isValid: boolean; errors: string[] };
}

export const useWorkflowStore = create<WorkflowStore>()(
  devtools(
    (set, get) => ({
      // Initial state
      workflow: null,
      selectedNodes: [],
      selectedConnections: [],
      isEditing: false,
      isDirty: false,
      zoom: 1,
      viewport: { x: 0, y: 0 },

      // Actions
      setWorkflow: (workflow) => {
        set({
          workflow,
          selectedNodes: [],
          selectedConnections: [],
          isDirty: false,
        });
      },

      updateWorkflow: (updates) => {
        const { workflow } = get();
        if (!workflow) return;

        set({
          workflow: { ...workflow, ...updates },
          isDirty: true,
        });
      },

      // Node operations
      addNode: (node) => {
        const { workflow } = get();
        if (!workflow) return;

        const updatedWorkflow = {
          ...workflow,
          nodes: [...workflow.nodes, node],
        };

        set({
          workflow: updatedWorkflow,
          isDirty: true,
        });
      },

      updateNode: (nodeKey, updates) => {
        const { workflow } = get();
        if (!workflow) return;

        const updatedNodes = workflow.nodes.map((node) =>
          node.key === nodeKey ? { ...node, ...updates } : node
        );

        set({
          workflow: { ...workflow, nodes: updatedNodes },
          isDirty: true,
        });
      },

      removeNode: (nodeKey) => {
        const { workflow } = get();
        if (!workflow) return;

        // Remove node
        const updatedNodes = workflow.nodes.filter((node) => node.key !== nodeKey);

        // Remove connections related to this node
        const updatedConnections = workflow.connections.filter(
          (conn) => conn.from_node !== nodeKey && conn.to_node !== nodeKey
        );

        // Remove from selection
        const updatedSelectedNodes = get().selectedNodes.filter((key) => key !== nodeKey);

        set({
          workflow: {
            ...workflow,
            nodes: updatedNodes,
            connections: updatedConnections,
          },
          selectedNodes: updatedSelectedNodes,
          isDirty: true,
        });
      },

      moveNode: (nodeKey, position) => {
        const { updateNode } = get();
        updateNode(nodeKey, { position });
      },

      // Connection operations
      addConnection: (connection) => {
        const { workflow } = get();
        if (!workflow) return;

        // Check if connection already exists
        const exists = workflow.connections.some(
          (conn) =>
            conn.from_node === connection.from_node &&
            conn.to_node === connection.to_node &&
            conn.from_port === connection.from_port &&
            conn.to_port === connection.to_port
        );

        if (exists) return;

        const updatedWorkflow = {
          ...workflow,
          connections: [...workflow.connections, connection],
        };

        set({
          workflow: updatedWorkflow,
          isDirty: true,
        });
      },

      removeConnection: (connectionId) => {
        const { workflow } = get();
        if (!workflow) return;

        const updatedConnections = workflow.connections.filter(
          (conn) => conn.id !== connectionId
        );

        // Remove from selection
        const updatedSelectedConnections = get().selectedConnections.filter(
          (id) => id !== connectionId
        );

        set({
          workflow: { ...workflow, connections: updatedConnections },
          selectedConnections: updatedSelectedConnections,
          isDirty: true,
        });
      },

      // Selection operations
      selectNode: (nodeKey, multiSelect = false) => {
        const { selectedNodes } = get();

        let updatedSelection: NodeKey[];
        if (multiSelect) {
          updatedSelection = selectedNodes.includes(nodeKey)
            ? selectedNodes.filter((key) => key !== nodeKey)
            : [...selectedNodes, nodeKey];
        } else {
          updatedSelection = [nodeKey];
        }

        set({
          selectedNodes: updatedSelection,
          selectedConnections: [], // Clear connection selection
        });
      },

      selectConnection: (connectionId, multiSelect = false) => {
        const { selectedConnections } = get();

        let updatedSelection: string[];
        if (multiSelect) {
          updatedSelection = selectedConnections.includes(connectionId)
            ? selectedConnections.filter((id) => id !== connectionId)
            : [...selectedConnections, connectionId];
        } else {
          updatedSelection = [connectionId];
        }

        set({
          selectedConnections: updatedSelection,
          selectedNodes: [], // Clear node selection
        });
      },

      clearSelection: () => {
        set({
          selectedNodes: [],
          selectedConnections: [],
        });
      },

      // Editor state
      setEditing: (isEditing) => set({ isEditing }),
      setDirty: (isDirty) => set({ isDirty }),
      setZoom: (zoom) => set({ zoom }),
      setViewport: (viewport) => set({ viewport }),

      // Utility functions
      getNode: (nodeKey) => {
        const { workflow } = get();
        return workflow?.nodes.find((node) => node.key === nodeKey);
      },

      getConnection: (connectionId) => {
        const { workflow } = get();
        return workflow?.connections.find((conn) => conn.id === connectionId);
      },

      getConnectedNodes: (nodeKey) => {
        const { workflow } = get();
        if (!workflow) return { incoming: [], outgoing: [] };

        const incoming: NodeKey[] = [];
        const outgoing: NodeKey[] = [];

        workflow.connections.forEach((conn) => {
          if (conn.to_node === nodeKey) {
            incoming.push(conn.from_node);
          }
          if (conn.from_node === nodeKey) {
            outgoing.push(conn.to_node);
          }
        });

        return { incoming, outgoing };
      },

      validateWorkflow: () => {
        const { workflow } = get();
        if (!workflow) {
          return { isValid: false, errors: ['No workflow loaded'] };
        }

        const errors: string[] = [];

        // Check for at least one node
        if (workflow.nodes.length === 0) {
          errors.push('Workflow must have at least one node');
        }

        // Check for entry and exit nodes
        const hasEntry = workflow.nodes.some((node) => node.type === 'entry');
        const hasExit = workflow.nodes.some((node) => node.type === 'exit');

        if (!hasEntry) {
          errors.push('Workflow must have an entry node');
        }
        if (!hasExit) {
          errors.push('Workflow must have an exit node');
        }

        // Validate connections
        workflow.connections.forEach((conn) => {
          const fromNode = workflow.nodes.find((node) => node.key === conn.from_node);
          const toNode = workflow.nodes.find((node) => node.key === conn.to_node);

          if (!fromNode) {
            errors.push(`Connection references non-existent from node: ${conn.from_node}`);
          }
          if (!toNode) {
            errors.push(`Connection references non-existent to node: ${conn.to_node}`);
          }
        });

        return {
          isValid: errors.length === 0,
          errors,
        };
      },
    }),
    {
      name: 'workflow-store',
    }
  )
);
