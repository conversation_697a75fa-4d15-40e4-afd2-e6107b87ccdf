import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import {
  WorkflowExecution,
  ExecutionEvent,
  ExecutionState,
  NodeKey,
  NodeExecutionStatus,
  ExecutionStatus,
} from '../types/workflow';

interface ExecutionStore extends ExecutionState {
  // Actions
  setExecution: (execution: WorkflowExecution | null) => void;
  updateExecution: (updates: Partial<WorkflowExecution>) => void;
  addEvent: (event: ExecutionEvent) => void;
  addLog: (log: string) => void;
  clearLogs: () => void;
  setRunning: (isRunning: boolean) => void;
  
  // Event stream management
  eventSource: EventSource | null;
  startEventStream: (executionId: string) => void;
  stopEventStream: () => void;
  
  // Utility functions
  getNodeExecutionStatus: (nodeKey: NodeKey) => NodeExecutionStatus | null;
  getNodeExecutionOutput: (nodeKey: NodeKey) => any;
  getNodeExecutionError: (nodeKey: NodeKey) => string | null;
  getExecutionProgress: () => { completed: number; total: number; percentage: number };
  reset: () => void;
}

export const useExecutionStore = create<ExecutionStore>()(
  devtools(
    (set, get) => ({
      // Initial state
      execution: null,
      events: [],
      isRunning: false,
      logs: [],
      eventSource: null,

      // Actions
      setExecution: (execution) => {
        set({
          execution,
          isRunning: execution?.status === 'running',
          events: [],
          logs: [],
        });
      },

      updateExecution: (updates) => {
        const { execution } = get();
        if (!execution) return;

        const updatedExecution = { ...execution, ...updates };
        
        set({
          execution: updatedExecution,
          isRunning: updatedExecution.status === 'running',
        });
      },

      addEvent: (event) => {
        const { events, addLog } = get();
        
        set({
          events: [...events, event],
        });

        // Add to logs
        const timestamp = new Date(event.timestamp).toLocaleTimeString();
        let logMessage = `[${timestamp}] ${event.type}`;
        
        if (event.data) {
          if (event.data.node_key) {
            logMessage += ` - Node: ${event.data.node_key}`;
          }
          if (event.data.error) {
            logMessage += ` - Error: ${event.data.error}`;
          }
          if (event.data.output) {
            logMessage += ` - Output: ${JSON.stringify(event.data.output)}`;
          }
        }
        
        addLog(logMessage);

        // Update execution based on event
        const { execution, updateExecution } = get();
        if (!execution) return;

        switch (event.type) {
          case 'workflow_completed':
            updateExecution({
              status: ExecutionStatus.SUCCESS,
              output: event.data?.output,
              completed_at: event.timestamp,
            });
            break;
          case 'workflow_failed':
            updateExecution({
              status: ExecutionStatus.FAILED,
              error: event.data?.error,
              completed_at: event.timestamp,
            });
            break;
          case 'workflow_cancelled':
            updateExecution({
              status: ExecutionStatus.CANCELLED,
              completed_at: event.timestamp,
            });
            break;
          case 'node_started':
          case 'node_completed':
          case 'node_failed':
          case 'node_skipped':
            // Update node execution in the execution object
            if (execution.node_executions && event.data?.node_key) {
              const nodeExecIndex = execution.node_executions.findIndex(
                (ne) => ne.node_key === event.data!.node_key
              );
              
              if (nodeExecIndex >= 0) {
                const updatedNodeExecutions = [...execution.node_executions];
                const nodeExec = { ...updatedNodeExecutions[nodeExecIndex] };
                
                switch (event.type) {
                  case 'node_started':
                    nodeExec.status = NodeExecutionStatus.RUNNING;
                    nodeExec.started_at = event.timestamp;
                    break;
                  case 'node_completed':
                    nodeExec.status = NodeExecutionStatus.SUCCESS;
                    nodeExec.output = event.data?.output;
                    nodeExec.completed_at = event.timestamp;
                    break;
                  case 'node_failed':
                    nodeExec.status = NodeExecutionStatus.FAILED;
                    nodeExec.error = event.data?.error;
                    nodeExec.completed_at = event.timestamp;
                    break;
                  case 'node_skipped':
                    nodeExec.status = NodeExecutionStatus.SKIPPED;
                    nodeExec.completed_at = event.timestamp;
                    break;
                }
                
                updatedNodeExecutions[nodeExecIndex] = nodeExec;
                updateExecution({ node_executions: updatedNodeExecutions });
              }
            }
            break;
        }
      },

      addLog: (log) => {
        const { logs } = get();
        set({
          logs: [...logs, log],
        });
      },

      clearLogs: () => {
        set({ logs: [] });
      },

      setRunning: (isRunning) => {
        set({ isRunning });
      },

      // Event stream management
      startEventStream: (executionId) => {
        const { stopEventStream, addEvent } = get();
        
        // Stop existing stream
        stopEventStream();
        
        // Create new event source
        const eventSource = new EventSource(`/api/v1/executions/${executionId}/events`);
        
        eventSource.onmessage = (event) => {
          try {
            const executionEvent: ExecutionEvent = JSON.parse(event.data);
            addEvent(executionEvent);
          } catch (error) {
            console.error('Failed to parse execution event:', error);
          }
        };
        
        eventSource.onerror = (error) => {
          console.error('EventSource error:', error);
          // Automatically reconnect after a delay
          setTimeout(() => {
            if (get().eventSource === eventSource) {
              get().startEventStream(executionId);
            }
          }, 5000);
        };
        
        set({ eventSource });
      },

      stopEventStream: () => {
        const { eventSource } = get();
        if (eventSource) {
          eventSource.close();
          set({ eventSource: null });
        }
      },

      // Utility functions
      getNodeExecutionStatus: (nodeKey) => {
        const { execution } = get();
        if (!execution?.node_executions) return null;
        
        const nodeExec = execution.node_executions.find((ne) => ne.node_key === nodeKey);
        return nodeExec?.status || null;
      },

      getNodeExecutionOutput: (nodeKey) => {
        const { execution } = get();
        if (!execution?.node_executions) return null;
        
        const nodeExec = execution.node_executions.find((ne) => ne.node_key === nodeKey);
        return nodeExec?.output || null;
      },

      getNodeExecutionError: (nodeKey) => {
        const { execution } = get();
        if (!execution?.node_executions) return null;
        
        const nodeExec = execution.node_executions.find((ne) => ne.node_key === nodeKey);
        return nodeExec?.error || null;
      },

      getExecutionProgress: () => {
        const { execution } = get();
        if (!execution?.node_executions) {
          return { completed: 0, total: 0, percentage: 0 };
        }
        
        const total = execution.node_executions.length;
        const completed = execution.node_executions.filter(
          (ne) => ne.status === 'success' || ne.status === 'failed' || ne.status === 'skipped'
        ).length;
        
        const percentage = total > 0 ? Math.round((completed / total) * 100) : 0;
        
        return { completed, total, percentage };
      },

      reset: () => {
        const { stopEventStream } = get();
        stopEventStream();
        
        set({
          execution: null,
          events: [],
          isRunning: false,
          logs: [],
          eventSource: null,
        });
      },
    }),
    {
      name: 'execution-store',
    }
  )
);
