// Workflow Engine Frontend Types

export type NodeKey = string;

export enum NodeType {
  // Control Flow
  ENTRY = 'entry',
  EXIT = 'exit',
  SELECTOR = 'selector',
  LOOP = 'loop',
  BATCH = 'batch',
  CONTINUE = 'continue',
  BREAK = 'break',

  // AI & LLM
  LLM = 'llm',

  // Data Processing
  VARIABLE_ASSIGNER = 'variable_assigner',
  TEXT_PROCESSOR = 'text_processor',
  VARIABLE_AGGREGATOR = 'variable_aggregator',

  // Integration
  HTTP_REQUESTER = 'http_requester',
  CODE_RUNNER = 'code_runner',
  SUB_WORKFLOW = 'sub_workflow',

  // Database
  DATABASE_QUERY = 'database_query',
  DATABASE_CREATE = 'database_create',
  DATABASE_UPDATE = 'database_update',
  DATABASE_DELETE = 'database_delete',

  // Knowledge & Data
  KNOWLEDGE_RETRIEVER = 'knowledge_retriever',
  KNOWLEDGE_WRITER = 'knowledge_writer',

  // Input/Output
  INPUT_RECEIVER = 'input_receiver',
  OUTPUT_EMITTER = 'output_emitter',
  QUESTION_ANSWER = 'question_answer',

  // Utilities
  INTENT_DETECTOR = 'intent_detector',
  COMMENT = 'comment',

  // New utility nodes
  EMAIL_SENDER = 'email_sender',
  FILE_PROCESSOR = 'file_processor',
  IMAGE_PROCESSOR = 'image_processor',
  WEB_SCRAPER = 'web_scraper',
  SCHEDULER = 'scheduler',
  NOTIFICATION = 'notification',
  DATA_TRANSFORMER = 'data_transformer',
  CONDITIONAL_ROUTER = 'conditional_router',
  LOOP_CONTROLLER = 'loop_controller',
  ERROR_HANDLER = 'error_handler',
}

export enum WorkflowStatus {
  DRAFT = 'draft',
  PUBLISHED = 'published',
  ARCHIVED = 'archived',
}

export enum ExecutionStatus {
  RUNNING = 'running',
  SUCCESS = 'success',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
  INTERRUPTED = 'interrupted',
}

export enum NodeExecutionStatus {
  RUNNING = 'running',
  SUCCESS = 'success',
  FAILED = 'failed',
  SKIPPED = 'skipped',
}

export interface Position {
  x: number;
  y: number;
}

export interface TypeInfo {
  type: string;
  description?: string;
  required?: boolean;
  default?: any;
  enum?: string[];
}

export interface FieldInfo {
  path: string[];
  source?: SourceInfo;
}

export interface SourceInfo {
  type: 'constant' | 'reference' | 'expression' | 'variable';
  ref?: ReferenceInfo;
  data?: any;
}

export interface ReferenceInfo {
  from_node_key: NodeKey;
  from_path: string;
}

export interface ExceptionConfig {
  timeout_ms?: number;
  max_retry?: number;
  process_type?: 'throw' | 'default' | 'skip';
  default_output?: any;
}

export interface StreamConfig {
  can_generate_stream?: boolean;
  require_streaming_input?: boolean;
}

export interface TokenUsage {
  input_tokens: number;
  output_tokens: number;
}

export interface NodeSchema {
  key: NodeKey;
  name: string;
  type: NodeType;
  description?: string;
  position?: Position;
  config?: any;
  input_types?: Record<string, TypeInfo>;
  input_sources?: FieldInfo[];
  output_types?: Record<string, TypeInfo>;
  output_sources?: FieldInfo[];
  exception_config?: ExceptionConfig;
  stream_config?: StreamConfig;
}

export interface Connection {
  id: string;
  from_node: NodeKey;
  to_node: NodeKey;
  from_port?: string;
  to_port?: string;
}

export interface WorkflowSchema {
  id: string;
  name: string;
  description?: string;
  version: string;
  status: WorkflowStatus;
  nodes: NodeSchema[];
  connections: Connection[];
  hierarchy?: Record<NodeKey, NodeKey>;
  created_at?: string;
  updated_at?: string;
}

export interface NodeExecution {
  id: string;
  node_key: NodeKey;
  node_name: string;
  node_type: NodeType;
  status: NodeExecutionStatus;
  input?: any;
  output?: any;
  raw_output?: any;
  error?: string;
  started_at: string;
  completed_at?: string;
  duration?: number;
  token_usage?: TokenUsage;
  retry_count?: number;
  metadata?: Record<string, any>;
}

export interface WorkflowExecution {
  id: string;
  workflow_id: string;
  version: string;
  status: ExecutionStatus;
  input?: any;
  output?: any;
  error?: string;
  started_at: string;
  completed_at?: string;
  duration?: number;
  token_usage?: TokenUsage;
  node_executions?: NodeExecution[];
  context?: Record<string, any>;
}

export interface ExecutionOptions {
  async?: boolean;
  timeout_seconds?: number;
  max_retries?: number;
  enable_streaming?: boolean;
  enable_debug?: boolean;
  context?: Record<string, any>;
}

export interface ExecutionEvent {
  id: string;
  execution_id: string;
  type: ExecutionEventType;
  timestamp: string;
  data?: Record<string, any>;
}

export enum ExecutionEventType {
  WORKFLOW_STARTED = 'workflow_started',
  WORKFLOW_COMPLETED = 'workflow_completed',
  WORKFLOW_FAILED = 'workflow_failed',
  WORKFLOW_CANCELLED = 'workflow_cancelled',
  NODE_STARTED = 'node_started',
  NODE_COMPLETED = 'node_completed',
  NODE_FAILED = 'node_failed',
  NODE_SKIPPED = 'node_skipped',
  STREAM_OUTPUT = 'stream_output',
}

// UI-specific types
export interface NodeUIData {
  label: string;
  color: string;
  icon?: string;
  category: string;
  description: string;
  inputs: Record<string, TypeInfo>;
  outputs: Record<string, TypeInfo>;
  configSchema?: any;
}

export interface WorkflowEditorState {
  workflow: WorkflowSchema | null;
  selectedNodes: NodeKey[];
  selectedConnections: string[];
  isEditing: boolean;
  isDirty: boolean;
  zoom: number;
  viewport: { x: number; y: number };
}

export interface ExecutionState {
  execution: WorkflowExecution | null;
  events: ExecutionEvent[];
  isRunning: boolean;
  logs: string[];
}

// API Response types
export interface ApiResponse<T> {
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
}

export interface CreateWorkflowRequest {
  name: string;
  description?: string;
}

export interface UpdateWorkflowRequest {
  name?: string;
  description?: string;
  nodes?: NodeSchema[];
  connections?: Connection[];
}

export interface ExecuteWorkflowRequest {
  input: Record<string, any>;
  options?: ExecutionOptions;
}
