import axios, { AxiosResponse } from 'axios';
import {
  WorkflowSchema,
  WorkflowExecution,
  CreateWorkflowRequest,
  UpdateWorkflowRequest,
  ExecuteWorkflowRequest,
  ApiResponse,
  PaginatedResponse,
  ExecutionEvent,
} from '../types/workflow';

// Create axios instance with base configuration
const api = axios.create({
  baseURL: '/api/v1',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for adding auth tokens if needed
api.interceptors.request.use(
  (config) => {
    // Add auth token if available
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for handling errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Handle unauthorized access
      localStorage.removeItem('auth_token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Workflow API
export const workflowApi = {
  // Create a new workflow
  create: async (data: CreateWorkflowRequest): Promise<ApiResponse<{ id: string }>> => {
    const response: AxiosResponse<ApiResponse<{ id: string }>> = await api.post('/workflows', data);
    return response.data;
  },

  // Get workflow by ID
  get: async (id: string): Promise<WorkflowSchema> => {
    const response: AxiosResponse<WorkflowSchema> = await api.get(`/workflows/${id}`);
    return response.data;
  },

  // Update workflow
  update: async (id: string, data: UpdateWorkflowRequest): Promise<ApiResponse<void>> => {
    const response: AxiosResponse<ApiResponse<void>> = await api.put(`/workflows/${id}`, data);
    return response.data;
  },

  // Delete workflow
  delete: async (id: string): Promise<ApiResponse<void>> => {
    const response: AxiosResponse<ApiResponse<void>> = await api.delete(`/workflows/${id}`);
    return response.data;
  },

  // List workflows with pagination
  list: async (params: {
    page?: number;
    limit?: number;
    status?: string;
  } = {}): Promise<PaginatedResponse<WorkflowSchema>> => {
    const response: AxiosResponse<PaginatedResponse<WorkflowSchema>> = await api.get('/workflows', {
      params,
    });
    return response.data;
  },

  // Validate workflow schema
  validate: async (schema: WorkflowSchema): Promise<ApiResponse<{ valid: boolean }>> => {
    const response: AxiosResponse<ApiResponse<{ valid: boolean }>> = await api.post('/workflows/validate', schema);
    return response.data;
  },

  // Execute workflow
  execute: async (id: string, data: ExecuteWorkflowRequest): Promise<WorkflowExecution> => {
    const response: AxiosResponse<WorkflowExecution> = await api.post(`/workflows/${id}/execute`, data);
    return response.data;
  },
};

// Execution API
export const executionApi = {
  // Get execution by ID
  get: async (id: string): Promise<WorkflowExecution> => {
    const response: AxiosResponse<WorkflowExecution> = await api.get(`/executions/${id}`);
    return response.data;
  },

  // Cancel execution
  cancel: async (id: string): Promise<ApiResponse<void>> => {
    const response: AxiosResponse<ApiResponse<void>> = await api.delete(`/executions/${id}`);
    return response.data;
  },

  // Get execution events stream
  getEventsStream: (id: string): EventSource => {
    return new EventSource(`/api/v1/executions/${id}/events`);
  },
};

// Health check
export const healthApi = {
  check: async (): Promise<{ status: string; service: string }> => {
    const response: AxiosResponse<{ status: string; service: string }> = await api.get('/health');
    return response.data;
  },
};

export default api;
