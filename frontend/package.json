{"name": "workflow-engine-frontend", "version": "1.0.0", "description": "Workflow Engine Frontend - Visual workflow designer and execution interface", "private": true, "dependencies": {"@types/node": "^18.0.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "typescript": "^4.9.0", "web-vitals": "^3.0.0", "axios": "^1.6.0", "react-router-dom": "^6.8.0", "reactflow": "^11.10.0", "@emotion/react": "^11.11.0", "@emotion/styled": "^11.11.0", "@mui/material": "^5.14.0", "@mui/icons-material": "^5.14.0", "@mui/lab": "^5.0.0-alpha.140", "react-hook-form": "^7.45.0", "@tanstack/react-query": "^4.32.0", "zustand": "^4.4.0", "lodash": "^4.17.21", "@types/lodash": "^4.14.195"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "type-check": "tsc --noEmit"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^5.0.0", "@typescript-eslint/parser": "^5.0.0", "eslint": "^8.0.0", "eslint-plugin-react": "^7.32.0", "eslint-plugin-react-hooks": "^4.6.0"}, "proxy": "http://localhost:8080"}