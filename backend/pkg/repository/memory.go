package repository

import (
	"context"
	"fmt"
	"sort"
	"strings"
	"sync"

	"workflow-engine/internal/domain/workflow"
)

// InMemoryRepository provides an in-memory implementation of WorkflowRepository
type InMemoryRepository struct {
	mu         sync.RWMutex
	workflows  map[string]*workflow.WorkflowSchema
	executions map[string]*workflow.WorkflowExecution
}

// NewInMemoryRepository creates a new in-memory repository
func NewInMemoryRepository() *InMemoryRepository {
	return &InMemoryRepository{
		workflows:  make(map[string]*workflow.WorkflowSchema),
		executions: make(map[string]*workflow.WorkflowExecution),
	}
}

// CreateWorkflow creates a new workflow
func (r *InMemoryRepository) CreateWorkflow(ctx context.Context, schema *workflow.WorkflowSchema) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	if _, exists := r.workflows[schema.ID]; exists {
		return fmt.Errorf("workflow with ID %s already exists", schema.ID)
	}

	// Create a copy to avoid external modifications
	copy := *schema
	r.workflows[schema.ID] = &copy

	return nil
}

// GetWorkflow retrieves a workflow by ID
func (r *InMemoryRepository) GetWorkflow(ctx context.Context, id string) (*workflow.WorkflowSchema, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	schema, exists := r.workflows[id]
	if !exists {
		return nil, fmt.Errorf("workflow with ID %s not found", id)
	}

	// Return a copy to avoid external modifications
	copy := *schema
	return &copy, nil
}

// UpdateWorkflow updates an existing workflow
func (r *InMemoryRepository) UpdateWorkflow(ctx context.Context, schema *workflow.WorkflowSchema) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	if _, exists := r.workflows[schema.ID]; !exists {
		return fmt.Errorf("workflow with ID %s not found", schema.ID)
	}

	// Create a copy to avoid external modifications
	copy := *schema
	r.workflows[schema.ID] = &copy

	return nil
}

// DeleteWorkflow deletes a workflow by ID
func (r *InMemoryRepository) DeleteWorkflow(ctx context.Context, id string) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	if _, exists := r.workflows[id]; !exists {
		return fmt.Errorf("workflow with ID %s not found", id)
	}

	delete(r.workflows, id)
	return nil
}

// ListWorkflows lists workflows with pagination and filtering
func (r *InMemoryRepository) ListWorkflows(ctx context.Context, page, limit int, status string) ([]*workflow.WorkflowSchema, int64, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	// Collect all workflows
	var allWorkflows []*workflow.WorkflowSchema
	for _, schema := range r.workflows {
		// Filter by status if specified
		if status != "" && string(schema.Status) != status {
			continue
		}

		// Create a copy
		copy := *schema
		allWorkflows = append(allWorkflows, &copy)
	}

	// Sort by creation time (newest first)
	sort.Slice(allWorkflows, func(i, j int) bool {
		if allWorkflows[i].CreatedAt == nil {
			return false
		}
		if allWorkflows[j].CreatedAt == nil {
			return true
		}
		return allWorkflows[i].CreatedAt.After(*allWorkflows[j].CreatedAt)
	})

	total := int64(len(allWorkflows))

	// Apply pagination
	start := (page - 1) * limit
	if start >= len(allWorkflows) {
		return []*workflow.WorkflowSchema{}, total, nil
	}

	end := start + limit
	if end > len(allWorkflows) {
		end = len(allWorkflows)
	}

	return allWorkflows[start:end], total, nil
}

// CreateExecution creates a new execution
func (r *InMemoryRepository) CreateExecution(ctx context.Context, execution *workflow.WorkflowExecution) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	if _, exists := r.executions[execution.ID]; exists {
		return fmt.Errorf("execution with ID %s already exists", execution.ID)
	}

	// Create a copy to avoid external modifications
	copy := *execution
	r.executions[execution.ID] = &copy

	return nil
}

// GetExecution retrieves an execution by ID
func (r *InMemoryRepository) GetExecution(ctx context.Context, id string) (*workflow.WorkflowExecution, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	execution, exists := r.executions[id]
	if !exists {
		return nil, fmt.Errorf("execution with ID %s not found", id)
	}

	// Return a copy to avoid external modifications
	copy := *execution
	return &copy, nil
}

// UpdateExecution updates an existing execution
func (r *InMemoryRepository) UpdateExecution(ctx context.Context, execution *workflow.WorkflowExecution) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	if _, exists := r.executions[execution.ID]; !exists {
		return fmt.Errorf("execution with ID %s not found", execution.ID)
	}

	// Create a copy to avoid external modifications
	copy := *execution
	r.executions[execution.ID] = &copy

	return nil
}

// ListExecutions lists executions for a workflow with pagination
func (r *InMemoryRepository) ListExecutions(ctx context.Context, workflowID string, page, limit int) ([]*workflow.WorkflowExecution, int64, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	// Collect executions for the specified workflow
	var allExecutions []*workflow.WorkflowExecution
	for _, execution := range r.executions {
		if workflowID == "" || execution.WorkflowID == workflowID {
			// Create a copy
			copy := *execution
			allExecutions = append(allExecutions, &copy)
		}
	}

	// Sort by start time (newest first)
	sort.Slice(allExecutions, func(i, j int) bool {
		return allExecutions[i].StartedAt.After(allExecutions[j].StartedAt)
	})

	total := int64(len(allExecutions))

	// Apply pagination
	start := (page - 1) * limit
	if start >= len(allExecutions) {
		return []*workflow.WorkflowExecution{}, total, nil
	}

	end := start + limit
	if end > len(allExecutions) {
		end = len(allExecutions)
	}

	return allExecutions[start:end], total, nil
}

// SearchWorkflows searches workflows by name or description
func (r *InMemoryRepository) SearchWorkflows(ctx context.Context, query string, page, limit int) ([]*workflow.WorkflowSchema, int64, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	query = strings.ToLower(query)
	var matchingWorkflows []*workflow.WorkflowSchema

	for _, schema := range r.workflows {
		// Search in name and description
		if strings.Contains(strings.ToLower(schema.Name), query) ||
			strings.Contains(strings.ToLower(schema.Description), query) {
			// Create a copy
			copy := *schema
			matchingWorkflows = append(matchingWorkflows, &copy)
		}
	}

	// Sort by creation time (newest first)
	sort.Slice(matchingWorkflows, func(i, j int) bool {
		if matchingWorkflows[i].CreatedAt == nil {
			return false
		}
		if matchingWorkflows[j].CreatedAt == nil {
			return true
		}
		return matchingWorkflows[i].CreatedAt.After(*matchingWorkflows[j].CreatedAt)
	})

	total := int64(len(matchingWorkflows))

	// Apply pagination
	start := (page - 1) * limit
	if start >= len(matchingWorkflows) {
		return []*workflow.WorkflowSchema{}, total, nil
	}

	end := start + limit
	if end > len(matchingWorkflows) {
		end = len(matchingWorkflows)
	}

	return matchingWorkflows[start:end], total, nil
}

// GetWorkflowStats returns statistics about workflows
func (r *InMemoryRepository) GetWorkflowStats(ctx context.Context) (map[string]interface{}, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	stats := make(map[string]interface{})
	
	// Count workflows by status
	statusCounts := make(map[workflow.WorkflowStatus]int)
	for _, schema := range r.workflows {
		statusCounts[schema.Status]++
	}

	// Count executions by status
	executionStatusCounts := make(map[workflow.ExecutionStatus]int)
	for _, execution := range r.executions {
		executionStatusCounts[execution.Status]++
	}

	stats["total_workflows"] = len(r.workflows)
	stats["total_executions"] = len(r.executions)
	stats["workflow_status_counts"] = statusCounts
	stats["execution_status_counts"] = executionStatusCounts

	return stats, nil
}
