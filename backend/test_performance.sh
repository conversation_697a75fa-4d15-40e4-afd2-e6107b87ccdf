#!/bin/bash

echo "Testing Performance Optimizations..."

# Test 1: Create a workflow for performance testing
echo "1. Creating performance test workflow..."
WORKFLOW_ID=$(curl -s -X POST http://localhost:8080/api/v1/workflows \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Performance Test Workflow",
    "description": "Test workflow for performance optimization features",
    "nodes": [
      {
        "key": "entry",
        "name": "Start",
        "type": "entry",
        "description": "Entry point",
        "config": {},
        "position": {"x": 100, "y": 100}
      },
      {
        "key": "llm1",
        "name": "AI Assistant 1",
        "type": "llm",
        "description": "First LLM processing",
        "config": {
          "model": "gpt-4o-mini",
          "system_prompt": "You are a helpful AI assistant.",
          "user_prompt": "Analyze this text: {{text}}",
          "temperature": 0.7,
          "max_tokens": 200
        },
        "position": {"x": 300, "y": 100}
      },
      {
        "key": "llm2",
        "name": "AI Assistant 2",
        "type": "llm",
        "description": "Second LLM processing",
        "config": {
          "model": "gpt-4o-mini",
          "system_prompt": "You are a helpful AI assistant.",
          "user_prompt": "Summarize this analysis: {{response}}",
          "temperature": 0.5,
          "max_tokens": 150
        },
        "position": {"x": 500, "y": 100}
      },
      {
        "key": "intent",
        "name": "Intent Detection",
        "type": "intent_detector",
        "description": "Detect user intent",
        "config": {},
        "position": {"x": 700, "y": 100}
      },
      {
        "key": "exit",
        "name": "End",
        "type": "exit",
        "description": "Exit point",
        "config": {},
        "position": {"x": 900, "y": 100}
      }
    ],
    "connections": [
      {
        "from_node": "entry",
        "to_node": "llm1"
      },
      {
        "from_node": "llm1",
        "to_node": "llm2"
      },
      {
        "from_node": "llm2",
        "to_node": "intent"
      },
      {
        "from_node": "intent",
        "to_node": "exit"
      }
    ]
  }' | jq -r '.id')

echo "Created workflow with ID: $WORKFLOW_ID"

# Test 2: Execute workflow multiple times to test caching and connection pooling
echo ""
echo "2. Testing caching and connection pooling with multiple executions..."

# Function to execute workflow and measure time
execute_workflow() {
  local iteration=$1
  local start_time=$(date +%s%N)
  
  curl -s -X POST http://localhost:8080/api/v1/workflows/$WORKFLOW_ID/execute \
    -H "Content-Type: application/json" \
    -d "{
      \"input\": {
        \"text\": \"This is test iteration $iteration for performance testing.\"
      }
    }" > /dev/null
  
  local end_time=$(date +%s%N)
  local duration=$(( (end_time - start_time) / 1000000 )) # Convert to milliseconds
  
  echo "Iteration $iteration: ${duration}ms"
}

# Execute workflow 5 times to test performance improvements
for i in {1..5}; do
  execute_workflow $i
done

# Test 3: Concurrent executions to test connection pooling
echo ""
echo "3. Testing concurrent executions (connection pooling)..."

# Function for concurrent execution
concurrent_execute() {
  local id=$1
  curl -s -X POST http://localhost:8080/api/v1/workflows/$WORKFLOW_ID/execute \
    -H "Content-Type: application/json" \
    -d "{
      \"input\": {
        \"text\": \"Concurrent test $id\"
      }
    }" > /dev/null &
}

# Start concurrent executions
echo "Starting 3 concurrent executions..."
start_time=$(date +%s%N)

concurrent_execute 1
concurrent_execute 2
concurrent_execute 3

# Wait for all background jobs to complete
wait

end_time=$(date +%s%N)
concurrent_duration=$(( (end_time - start_time) / 1000000 ))
echo "All concurrent executions completed in: ${concurrent_duration}ms"

echo ""
echo "Performance testing completed!"
echo ""
echo "Note: Since no real API keys are configured, all tests use fallback responses."
echo "In production with real APIs, you would see significant performance improvements from:"
echo "- Connection pooling (reused HTTP connections)"
echo "- Response caching (faster subsequent requests)"
echo "- Retry mechanisms (better reliability)"
echo "- Parallel execution (where applicable)"
