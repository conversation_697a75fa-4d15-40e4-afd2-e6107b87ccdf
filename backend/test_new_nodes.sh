#!/bin/bash

echo "Testing New Node Types..."

# Test 1: Email Sender Node
echo "1. Testing Email Sender Node..."
EMAIL_WORKFLOW_ID=$(curl -s -X POST http://localhost:8080/api/v1/workflows \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Email Notification Workflow",
    "description": "Test email sender node",
    "nodes": [
      {
        "key": "entry",
        "name": "Start",
        "type": "entry",
        "description": "Entry point",
        "config": {},
        "position": {"x": 100, "y": 100}
      },
      {
        "key": "email",
        "name": "Send Email",
        "type": "email_sender",
        "description": "Send notification email",
        "config": {
          "to": "<EMAIL>",
          "subject": "Workflow Notification: {{status}}",
          "body": "Hello! Your workflow has completed with status: {{status}}. Result: {{result}}"
        },
        "position": {"x": 300, "y": 100}
      },
      {
        "key": "exit",
        "name": "End",
        "type": "exit",
        "description": "Exit point",
        "config": {},
        "position": {"x": 500, "y": 100}
      }
    ],
    "connections": [
      {
        "from_node": "entry",
        "to_node": "email"
      },
      {
        "from_node": "email",
        "to_node": "exit"
      }
    ]
  }' | jq -r '.id')

echo "Created email workflow: $EMAIL_WORKFLOW_ID"

curl -s -X POST http://localhost:8080/api/v1/workflows/$EMAIL_WORKFLOW_ID/execute \
  -H "Content-Type: application/json" \
  -d '{
    "input": {
      "status": "SUCCESS",
      "result": "All tasks completed successfully"
    }
  }' | jq '.node_executions[] | select(.node_type == "email_sender") | {status, output: .output}'

echo ""

# Test 2: Data Transformer Node
echo "2. Testing Data Transformer Node..."
TRANSFORM_WORKFLOW_ID=$(curl -s -X POST http://localhost:8080/api/v1/workflows \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Data Transformation Workflow",
    "description": "Test data transformer node",
    "nodes": [
      {
        "key": "entry",
        "name": "Start",
        "type": "entry",
        "description": "Entry point",
        "config": {},
        "position": {"x": 100, "y": 100}
      },
      {
        "key": "transform",
        "name": "Transform Data",
        "type": "data_transformer",
        "description": "Transform text to uppercase",
        "config": {
          "transform_type": "uppercase",
          "source_field": "message",
          "target_field": "message_upper"
        },
        "position": {"x": 300, "y": 100}
      },
      {
        "key": "exit",
        "name": "End",
        "type": "exit",
        "description": "Exit point",
        "config": {},
        "position": {"x": 500, "y": 100}
      }
    ],
    "connections": [
      {
        "from_node": "entry",
        "to_node": "transform"
      },
      {
        "from_node": "transform",
        "to_node": "exit"
      }
    ]
  }' | jq -r '.id')

echo "Created transform workflow: $TRANSFORM_WORKFLOW_ID"

curl -s -X POST http://localhost:8080/api/v1/workflows/$TRANSFORM_WORKFLOW_ID/execute \
  -H "Content-Type: application/json" \
  -d '{
    "input": {
      "message": "hello world from workflow engine"
    }
  }' | jq '.node_executions[] | select(.node_type == "data_transformer") | {status, input: .input.message, output: .output.message_upper}'

echo ""

# Test 3: Conditional Router Node
echo "3. Testing Conditional Router Node..."
ROUTER_WORKFLOW_ID=$(curl -s -X POST http://localhost:8080/api/v1/workflows \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Conditional Routing Workflow",
    "description": "Test conditional router node",
    "nodes": [
      {
        "key": "entry",
        "name": "Start",
        "type": "entry",
        "description": "Entry point",
        "config": {},
        "position": {"x": 100, "y": 100}
      },
      {
        "key": "router",
        "name": "Route Decision",
        "type": "conditional_router",
        "description": "Route based on score",
        "config": {
          "condition_field": "score",
          "operator": "greater_than",
          "condition_value": 80
        },
        "position": {"x": 300, "y": 100}
      },
      {
        "key": "exit",
        "name": "End",
        "type": "exit",
        "description": "Exit point",
        "config": {},
        "position": {"x": 500, "y": 100}
      }
    ],
    "connections": [
      {
        "from_node": "entry",
        "to_node": "router"
      },
      {
        "from_node": "router",
        "to_node": "exit"
      }
    ]
  }' | jq -r '.id')

echo "Created router workflow: $ROUTER_WORKFLOW_ID"

echo "Testing with high score (should pass condition):"
curl -s -X POST http://localhost:8080/api/v1/workflows/$ROUTER_WORKFLOW_ID/execute \
  -H "Content-Type: application/json" \
  -d '{
    "input": {
      "score": 95,
      "user": "Alice"
    }
  }' | jq '.node_executions[] | select(.node_type == "conditional_router") | {status, condition_met: .output.condition_met, route: .output.route}'

echo ""
echo "Testing with low score (should fail condition):"
curl -s -X POST http://localhost:8080/api/v1/workflows/$ROUTER_WORKFLOW_ID/execute \
  -H "Content-Type: application/json" \
  -d '{
    "input": {
      "score": 65,
      "user": "Bob"
    }
  }' | jq '.node_executions[] | select(.node_type == "conditional_router") | {status, condition_met: .output.condition_met, route: .output.route}'

echo ""

# Test 4: Image Processor Node
echo "4. Testing Image Processor Node..."
IMAGE_WORKFLOW_ID=$(curl -s -X POST http://localhost:8080/api/v1/workflows \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Image Processing Workflow",
    "description": "Test image processor node",
    "nodes": [
      {
        "key": "entry",
        "name": "Start",
        "type": "entry",
        "description": "Entry point",
        "config": {},
        "position": {"x": 100, "y": 100}
      },
      {
        "key": "image_resize",
        "name": "Resize Image",
        "type": "image_processor",
        "description": "Resize image to specific dimensions",
        "config": {
          "operation": "resize",
          "width": 1024,
          "height": 768
        },
        "position": {"x": 300, "y": 100}
      },
      {
        "key": "exit",
        "name": "End",
        "type": "exit",
        "description": "Exit point",
        "config": {},
        "position": {"x": 500, "y": 100}
      }
    ],
    "connections": [
      {
        "from_node": "entry",
        "to_node": "image_resize"
      },
      {
        "from_node": "image_resize",
        "to_node": "exit"
      }
    ]
  }' | jq -r '.id')

echo "Created image workflow: $IMAGE_WORKFLOW_ID"

curl -s -X POST http://localhost:8080/api/v1/workflows/$IMAGE_WORKFLOW_ID/execute \
  -H "Content-Type: application/json" \
  -d '{
    "input": {
      "image_url": "https://example.com/photo.jpg"
    }
  }' | jq '.node_executions[] | select(.node_type == "image_processor") | {status, operation: .output.operation, new_width: .output.new_width, new_height: .output.new_height, output_url: .output.output_url}'

echo ""

# Test 5: Web Scraper Node
echo "5. Testing Web Scraper Node..."
SCRAPER_WORKFLOW_ID=$(curl -s -X POST http://localhost:8080/api/v1/workflows \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Web Scraping Workflow",
    "description": "Test web scraper node",
    "nodes": [
      {
        "key": "entry",
        "name": "Start",
        "type": "entry",
        "description": "Entry point",
        "config": {},
        "position": {"x": 100, "y": 100}
      },
      {
        "key": "scraper",
        "name": "Scrape Website",
        "type": "web_scraper",
        "description": "Scrape content from website",
        "config": {
          "url": "https://example.com",
          "selector": "h1"
        },
        "position": {"x": 300, "y": 100}
      },
      {
        "key": "exit",
        "name": "End",
        "type": "exit",
        "description": "Exit point",
        "config": {},
        "position": {"x": 500, "y": 100}
      }
    ],
    "connections": [
      {
        "from_node": "entry",
        "to_node": "scraper"
      },
      {
        "from_node": "scraper",
        "to_node": "exit"
      }
    ]
  }' | jq -r '.id')

echo "Created scraper workflow: $SCRAPER_WORKFLOW_ID"

curl -s -X POST http://localhost:8080/api/v1/workflows/$SCRAPER_WORKFLOW_ID/execute \
  -H "Content-Type: application/json" \
  -d '{"input": {}}' | jq '.node_executions[] | select(.node_type == "web_scraper") | {status, url: .output.url, title: .output.title, content: .output.content}'

echo ""
echo "New node types testing completed!"
echo ""
echo "Summary of tested nodes:"
echo "✅ Email Sender - Send notifications via email"
echo "✅ Data Transformer - Transform data (uppercase, lowercase, etc.)"
echo "✅ Conditional Router - Route based on conditions"
echo "✅ Image Processor - Process images (resize, crop, filter, analyze)"
echo "✅ Web Scraper - Extract content from websites"
echo ""
echo "Additional available nodes (not tested in this script):"
echo "• File Processor - Read, write, delete files"
echo "• Scheduler - Schedule tasks with delays or specific times"
echo "• Notification - Send notifications to multiple channels"
echo "• Error Handler - Handle errors with different strategies"
