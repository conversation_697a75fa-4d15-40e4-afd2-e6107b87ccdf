package routes

import (
	"github.com/gin-gonic/gin"
	"workflow-engine/internal/api/handlers"
	"workflow-engine/internal/service"
)

// SetupRoutes configures all API routes
func SetupRoutes(workflowService *service.WorkflowService) *gin.Engine {
	r := gin.Default()

	// Add CORS middleware
	r.Use(func(c *gin.Context) {
		c.<PERSON><PERSON>("Access-Control-Allow-Origin", "*")
		c.<PERSON><PERSON>("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.<PERSON><PERSON>("Access-Control-Allow-Headers", "Content-Type, Authorization")
		
		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}
		
		c.Next()
	})

	// Health check
	r.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status":  "ok",
			"service": "workflow-engine",
		})
	})

	// Create handlers
	workflowHandler := handlers.NewWorkflowHandler(workflowService)

	// API v1 routes
	v1 := r.Group("/api/v1")
	{
		// Workflow management routes
		workflows := v1.Group("/workflows")
		{
			workflows.POST("", workflowHandler.CreateWorkflow)
			workflows.GET("", workflowHandler.ListWorkflows)
			workflows.GET("/:id", workflowHandler.GetWorkflow)
			workflows.PUT("/:id", workflowHandler.UpdateWorkflow)
			workflows.DELETE("/:id", workflowHandler.DeleteWorkflow)
			workflows.POST("/validate", workflowHandler.ValidateWorkflow)
			
			// Execution routes
			workflows.POST("/:id/execute", workflowHandler.ExecuteWorkflow)
		}

		// Execution management routes
		executions := v1.Group("/executions")
		{
			executions.GET("/:id", workflowHandler.GetExecution)
			executions.DELETE("/:id", workflowHandler.CancelExecution)
			executions.GET("/:id/events", workflowHandler.GetExecutionEvents)
		}
	}

	return r
}
