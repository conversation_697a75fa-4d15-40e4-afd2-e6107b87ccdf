package handlers

import (
	"net/http"
	"strconv"

	"workflow-engine/internal/domain/workflow"
	"workflow-engine/internal/service"

	"github.com/gin-gonic/gin"
)

// WorkflowHandler handles workflow-related HTTP requests
type WorkflowHandler struct {
	workflowService *service.WorkflowService
}

// NewWorkflowHandler creates a new workflow handler
func NewWorkflowHandler(workflowService *service.WorkflowService) *WorkflowHandler {
	return &WorkflowHandler{
		workflowService: workflowService,
	}
}

// CreateWorkflowRequest represents the request to create a workflow
type CreateWorkflowRequest struct {
	Name        string                 `json:"name" binding:"required"`
	Description string                 `json:"description"`
	Nodes       []*workflow.NodeSchema `json:"nodes"`
	Connections []*workflow.Connection `json:"connections"`
}

// CreateWorkflowResponse represents the response after creating a workflow
type CreateWorkflowResponse struct {
	ID      string `json:"id"`
	Message string `json:"message"`
}

// CreateWorkflow creates a new workflow
func (h *WorkflowHandler) CreateWorkflow(c *gin.Context) {
	var req CreateWorkflowRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	schema := &workflow.WorkflowSchema{
		Name:        req.Name,
		Description: req.Description,
		Version:     "1.0.0",
		Status:      workflow.WorkflowStatusDraft,
		Nodes:       req.Nodes,
		Connections: req.Connections,
	}

	id, err := h.workflowService.CreateWorkflow(c.Request.Context(), schema)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, CreateWorkflowResponse{
		ID:      id,
		Message: "Workflow created successfully",
	})
}

// GetWorkflow retrieves a workflow by ID
func (h *WorkflowHandler) GetWorkflow(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "workflow ID is required"})
		return
	}

	schema, err := h.workflowService.GetWorkflow(c.Request.Context(), id)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, schema)
}

// UpdateWorkflowRequest represents the request to update a workflow
type UpdateWorkflowRequest struct {
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Nodes       []*workflow.NodeSchema `json:"nodes"`
	Connections []*workflow.Connection `json:"connections"`
}

// UpdateWorkflow updates an existing workflow
func (h *WorkflowHandler) UpdateWorkflow(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "workflow ID is required"})
		return
	}

	var req UpdateWorkflowRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get existing workflow
	schema, err := h.workflowService.GetWorkflow(c.Request.Context(), id)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		return
	}

	// Update fields
	if req.Name != "" {
		schema.Name = req.Name
	}
	if req.Description != "" {
		schema.Description = req.Description
	}
	if req.Nodes != nil {
		schema.Nodes = req.Nodes
	}
	if req.Connections != nil {
		schema.Connections = req.Connections
	}

	err = h.workflowService.UpdateWorkflow(c.Request.Context(), schema)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Workflow updated successfully"})
}

// DeleteWorkflow deletes a workflow by ID
func (h *WorkflowHandler) DeleteWorkflow(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "workflow ID is required"})
		return
	}

	err := h.workflowService.DeleteWorkflow(c.Request.Context(), id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Workflow deleted successfully"})
}

// ListWorkflows lists all workflows with pagination
func (h *WorkflowHandler) ListWorkflows(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	status := c.Query("status")

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}

	workflows, total, err := h.workflowService.ListWorkflows(c.Request.Context(), page, limit, status)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"workflows": workflows,
		"total":     total,
		"page":      page,
		"limit":     limit,
	})
}

// ExecuteWorkflowRequest represents the request to execute a workflow
type ExecuteWorkflowRequest struct {
	Input   map[string]interface{}     `json:"input"`
	Options *workflow.ExecutionOptions `json:"options"`
}

// ExecuteWorkflow executes a workflow
func (h *WorkflowHandler) ExecuteWorkflow(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "workflow ID is required"})
		return
	}

	var req ExecuteWorkflowRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	execution, err := h.workflowService.ExecuteWorkflow(c.Request.Context(), id, req.Input, req.Options)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, execution)
}

// GetExecution retrieves an execution by ID
func (h *WorkflowHandler) GetExecution(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "execution ID is required"})
		return
	}

	execution, err := h.workflowService.GetExecution(c.Request.Context(), id)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, execution)
}

// CancelExecution cancels a running execution
func (h *WorkflowHandler) CancelExecution(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "execution ID is required"})
		return
	}

	err := h.workflowService.CancelExecution(c.Request.Context(), id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Execution cancelled successfully"})
}

// GetExecutionEvents streams execution events via Server-Sent Events
func (h *WorkflowHandler) GetExecutionEvents(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "execution ID is required"})
		return
	}

	// Set headers for SSE
	c.Header("Content-Type", "text/event-stream")
	c.Header("Cache-Control", "no-cache")
	c.Header("Connection", "keep-alive")
	c.Header("Access-Control-Allow-Origin", "*")

	// Get event stream
	eventChan, err := h.workflowService.GetExecutionEvents(c.Request.Context(), id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Stream events
	for {
		select {
		case event := <-eventChan:
			if event == nil {
				return
			}
			c.SSEvent("event", event)
			c.Writer.Flush()
		case <-c.Request.Context().Done():
			return
		}
	}
}

// ValidateWorkflow validates a workflow schema
func (h *WorkflowHandler) ValidateWorkflow(c *gin.Context) {
	var schema workflow.WorkflowSchema
	if err := c.ShouldBindJSON(&schema); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := schema.Validate(); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"valid": false,
			"error": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"valid":   true,
		"message": "Workflow schema is valid",
	})
}
