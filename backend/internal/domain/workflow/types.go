package workflow

// <PERSON><PERSON><PERSON><PERSON> represents a unique identifier for a node
type <PERSON><PERSON><PERSON><PERSON> string

// NodeType represents the type of a workflow node
type NodeType string

// Node type constants
const (
	// Control Flow
	NodeTypeEntry    NodeType = "entry"
	NodeTypeExit     NodeType = "exit"
	NodeTypeSelector NodeType = "selector"
	NodeTypeLoop     NodeType = "loop"
	NodeTypeBatch    NodeType = "batch"
	NodeTypeContinue NodeType = "continue"
	NodeTypeBreak    NodeType = "break"

	// AI & LLM
	NodeTypeLLM NodeType = "llm"

	// Data Processing
	NodeTypeVariableAssigner   NodeType = "variable_assigner"
	NodeTypeTextProcessor      NodeType = "text_processor"
	NodeTypeVariableAggregator NodeType = "variable_aggregator"

	// Integration
	NodeTypeHTTPRequester NodeType = "http_requester"
	NodeTypeCodeRunner    NodeType = "code_runner"
	NodeTypeSubWorkflow   NodeType = "sub_workflow"

	// Database
	NodeTypeDatabaseQuery  NodeType = "database_query"
	NodeTypeDatabaseCreate NodeType = "database_create"
	NodeTypeDatabaseUpdate NodeType = "database_update"
	NodeTypeDatabaseDelete NodeType = "database_delete"

	// Knowledge & Data
	NodeTypeKnowledgeRetriever NodeType = "knowledge_retriever"
	NodeTypeKnowledgeWriter    NodeType = "knowledge_writer"

	// Input/Output
	NodeTypeInputReceiver  NodeType = "input_receiver"
	NodeTypeOutputEmitter  NodeType = "output_emitter"
	NodeTypeQuestionAnswer NodeType = "question_answer"

	// Utilities
	NodeTypeIntentDetector NodeType = "intent_detector"
	NodeTypeComment        NodeType = "comment"

	// New utility nodes
	NodeTypeEmailSender       NodeType = "email_sender"
	NodeTypeFileProcessor     NodeType = "file_processor"
	NodeTypeImageProcessor    NodeType = "image_processor"
	NodeTypeWebScraper        NodeType = "web_scraper"
	NodeTypeScheduler         NodeType = "scheduler"
	NodeTypeNotification      NodeType = "notification"
	NodeTypeDataTransformer   NodeType = "data_transformer"
	NodeTypeConditionalRouter NodeType = "conditional_router"
	NodeTypeLoopController    NodeType = "loop_controller"
	NodeTypeErrorHandler      NodeType = "error_handler"
)

// WorkflowStatus represents the status of a workflow
type WorkflowStatus string

const (
	WorkflowStatusDraft     WorkflowStatus = "draft"
	WorkflowStatusPublished WorkflowStatus = "published"
	WorkflowStatusArchived  WorkflowStatus = "archived"
)

// ExecutionStatus represents the status of workflow execution
type ExecutionStatus string

const (
	ExecutionStatusRunning     ExecutionStatus = "running"
	ExecutionStatusSuccess     ExecutionStatus = "success"
	ExecutionStatusFailed      ExecutionStatus = "failed"
	ExecutionStatusCancelled   ExecutionStatus = "cancelled"
	ExecutionStatusInterrupted ExecutionStatus = "interrupted"
)

// NodeExecutionStatus represents the status of node execution
type NodeExecutionStatus string

const (
	NodeExecutionStatusRunning NodeExecutionStatus = "running"
	NodeExecutionStatusSuccess NodeExecutionStatus = "success"
	NodeExecutionStatusFailed  NodeExecutionStatus = "failed"
	NodeExecutionStatusSkipped NodeExecutionStatus = "skipped"
)

// ErrorProcessType defines how to handle errors
type ErrorProcessType string

const (
	ErrorProcessTypeThrow   ErrorProcessType = "throw"
	ErrorProcessTypeDefault ErrorProcessType = "default"
	ErrorProcessTypeSkip    ErrorProcessType = "skip"
)

// TypeInfo represents type information for inputs/outputs
type TypeInfo struct {
	Type        string      `json:"type"`
	Description string      `json:"description,omitempty"`
	Required    bool        `json:"required,omitempty"`
	Default     interface{} `json:"default,omitempty"`
	Enum        []string    `json:"enum,omitempty"`
}

// FieldInfo represents field mapping information
type FieldInfo struct {
	Path   []string    `json:"path"`
	Source *SourceInfo `json:"source,omitempty"`
}

// SourceInfo represents the source of a field value
type SourceInfo struct {
	Type string         `json:"type"` // "constant", "reference", "expression"
	Ref  *ReferenceInfo `json:"ref,omitempty"`
	Data interface{}    `json:"data,omitempty"`
}

// ReferenceInfo represents a reference to another node's output
type ReferenceInfo struct {
	FromNodeKey NodeKey `json:"from_node_key"`
	FromPath    string  `json:"from_path"`
}

// ExceptionConfig represents error handling configuration
type ExceptionConfig struct {
	TimeoutMS     int64            `json:"timeout_ms,omitempty"`
	MaxRetry      int64            `json:"max_retry,omitempty"`
	ProcessType   ErrorProcessType `json:"process_type,omitempty"`
	DefaultOutput interface{}      `json:"default_output,omitempty"`
}

// StreamConfig represents streaming configuration
type StreamConfig struct {
	CanGenerateStream     bool `json:"can_generate_stream,omitempty"`
	RequireStreamingInput bool `json:"require_streaming_input,omitempty"`
}

// TokenUsage represents token consumption information
type TokenUsage struct {
	InputTokens  int64 `json:"input_tokens"`
	OutputTokens int64 `json:"output_tokens"`
}
