package workflow

import (
	"context"
	"fmt"
	"strings"
	"time"
)

// EmailSenderExecutor executes email sending nodes
type EmailSenderExecutor struct{}

func (e *EmailSenderExecutor) Execute(ctx context.Context, node *NodeSchema, input map[string]interface{}) (map[string]interface{}, error) {
	config, ok := node.Config.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid email configuration")
	}

	// Get email configuration
	to, ok := config["to"].(string)
	if !ok {
		return nil, fmt.Errorf("recipient email is required")
	}

	subject, ok := config["subject"].(string)
	if !ok {
		subject = "Workflow Notification"
	}

	// Build email body from template
	bodyTemplate, ok := config["body"].(string)
	if !ok {
		bodyTemplate = "Workflow execution result: {{result}}"
	}

	// Simple template rendering
	body := bodyTemplate
	for key, value := range input {
		placeholder := fmt.Sprintf("{{%s}}", key)
		body = strings.ReplaceAll(body, placeholder, fmt.Sprintf("%v", value))
	}

	// Mock email sending (in real implementation, use SMTP)
	messageID := fmt.Sprintf("email_%d", time.Now().Unix())

	return map[string]interface{}{
		"success":    true,
		"message_id": messageID,
		"to":         to,
		"subject":    subject,
		"sent_at":    time.Now().Format(time.RFC3339),
	}, nil
}

func (e *EmailSenderExecutor) GetType() NodeType {
	return NodeTypeEmailSender
}

// FileProcessorExecutor executes file processing nodes
type FileProcessorExecutor struct{}

func (e *FileProcessorExecutor) Execute(ctx context.Context, node *NodeSchema, input map[string]interface{}) (map[string]interface{}, error) {
	config, ok := node.Config.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid file processor configuration")
	}

	// Get operation type
	operation, ok := config["operation"].(string)
	if !ok {
		return nil, fmt.Errorf("operation is required")
	}

	// Get file path or content
	var filePath string
	var fileContent string

	if path, ok := input["file_path"].(string); ok {
		filePath = path
	}
	if content, ok := input["file_content"].(string); ok {
		fileContent = content
	}

	// Process based on operation
	switch operation {
	case "read":
		if filePath == "" {
			return nil, fmt.Errorf("file_path is required for read operation")
		}
		// Mock file reading
		return map[string]interface{}{
			"content":   fmt.Sprintf("Mock content from file: %s", filePath),
			"size":      1024,
			"mime_type": "text/plain",
		}, nil

	case "write":
		if filePath == "" || fileContent == "" {
			return nil, fmt.Errorf("file_path and file_content are required for write operation")
		}
		// Mock file writing
		return map[string]interface{}{
			"success":       true,
			"bytes_written": len(fileContent),
			"file_path":     filePath,
		}, nil

	case "delete":
		if filePath == "" {
			return nil, fmt.Errorf("file_path is required for delete operation")
		}
		// Mock file deletion
		return map[string]interface{}{
			"success":    true,
			"file_path":  filePath,
			"deleted_at": time.Now().Format(time.RFC3339),
		}, nil

	default:
		return nil, fmt.Errorf("unsupported operation: %s", operation)
	}
}

func (e *FileProcessorExecutor) GetType() NodeType {
	return NodeTypeFileProcessor
}

// WebScraperExecutor executes web scraping nodes
type WebScraperExecutor struct{}

func (e *WebScraperExecutor) Execute(ctx context.Context, node *NodeSchema, input map[string]interface{}) (map[string]interface{}, error) {
	config, ok := node.Config.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid web scraper configuration")
	}

	// Get URL
	url, ok := config["url"].(string)
	if !ok {
		return nil, fmt.Errorf("url is required")
	}

	// Get selector (optional)
	selector, _ := config["selector"].(string)

	// Mock web scraping
	scrapedData := map[string]interface{}{
		"url":        url,
		"title":      "Mock Page Title",
		"content":    "Mock scraped content from " + url,
		"links":      []string{"http://example.com/link1", "http://example.com/link2"},
		"images":     []string{"http://example.com/image1.jpg"},
		"scraped_at": time.Now().Format(time.RFC3339),
	}

	if selector != "" {
		scrapedData["selector"] = selector
		scrapedData["selected_content"] = fmt.Sprintf("Content selected by: %s", selector)
	}

	return scrapedData, nil
}

func (e *WebScraperExecutor) GetType() NodeType {
	return NodeTypeWebScraper
}

// NotificationExecutor executes notification nodes
type NotificationExecutor struct{}

func (e *NotificationExecutor) Execute(ctx context.Context, node *NodeSchema, input map[string]interface{}) (map[string]interface{}, error) {
	config, ok := node.Config.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid notification configuration")
	}

	// Get notification type
	notificationType, ok := config["type"].(string)
	if !ok {
		notificationType = "info"
	}

	// Get message
	message, ok := config["message"].(string)
	if !ok {
		message = "Workflow notification"
	}

	// Render message with input variables
	for key, value := range input {
		placeholder := fmt.Sprintf("{{%s}}", key)
		message = strings.ReplaceAll(message, placeholder, fmt.Sprintf("%v", value))
	}

	// Get channels
	channels := []string{"console"}
	if configChannels, ok := config["channels"].([]interface{}); ok {
		channels = make([]string, len(configChannels))
		for i, ch := range configChannels {
			if chStr, ok := ch.(string); ok {
				channels[i] = chStr
			}
		}
	}

	// Mock notification sending
	notificationID := fmt.Sprintf("notif_%d", time.Now().Unix())

	return map[string]interface{}{
		"success":         true,
		"notification_id": notificationID,
		"type":            notificationType,
		"message":         message,
		"channels":        channels,
		"sent_at":         time.Now().Format(time.RFC3339),
	}, nil
}

func (e *NotificationExecutor) GetType() NodeType {
	return NodeTypeNotification
}

// DataTransformerExecutor executes data transformation nodes
type DataTransformerExecutor struct{}

func (e *DataTransformerExecutor) Execute(ctx context.Context, node *NodeSchema, input map[string]interface{}) (map[string]interface{}, error) {
	config, ok := node.Config.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid data transformer configuration")
	}

	// Get transformation type
	transformType, ok := config["transform_type"].(string)
	if !ok {
		return nil, fmt.Errorf("transform_type is required")
	}

	// Get source field
	sourceField, ok := config["source_field"].(string)
	if !ok {
		return nil, fmt.Errorf("source_field is required")
	}

	// Get target field
	targetField, ok := config["target_field"].(string)
	if !ok {
		targetField = sourceField + "_transformed"
	}

	// Get source value
	sourceValue, exists := input[sourceField]
	if !exists {
		return nil, fmt.Errorf("source field %s not found in input", sourceField)
	}

	// Transform data based on type
	var transformedValue interface{}
	switch transformType {
	case "uppercase":
		if str, ok := sourceValue.(string); ok {
			transformedValue = strings.ToUpper(str)
		} else {
			transformedValue = strings.ToUpper(fmt.Sprintf("%v", sourceValue))
		}

	case "lowercase":
		if str, ok := sourceValue.(string); ok {
			transformedValue = strings.ToLower(str)
		} else {
			transformedValue = strings.ToLower(fmt.Sprintf("%v", sourceValue))
		}

	case "length":
		if str, ok := sourceValue.(string); ok {
			transformedValue = len(str)
		} else {
			transformedValue = len(fmt.Sprintf("%v", sourceValue))
		}

	case "reverse":
		str := fmt.Sprintf("%v", sourceValue)
		runes := []rune(str)
		for i, j := 0, len(runes)-1; i < j; i, j = i+1, j-1 {
			runes[i], runes[j] = runes[j], runes[i]
		}
		transformedValue = string(runes)

	default:
		return nil, fmt.Errorf("unsupported transform type: %s", transformType)
	}

	// Create output with transformed value
	output := make(map[string]interface{})
	for k, v := range input {
		output[k] = v
	}
	output[targetField] = transformedValue

	return output, nil
}

func (e *DataTransformerExecutor) GetType() NodeType {
	return NodeTypeDataTransformer
}

// ConditionalRouterExecutor executes conditional routing nodes
type ConditionalRouterExecutor struct{}

func (e *ConditionalRouterExecutor) Execute(ctx context.Context, node *NodeSchema, input map[string]interface{}) (map[string]interface{}, error) {
	config, ok := node.Config.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid conditional router configuration")
	}

	// Get condition field
	conditionField, ok := config["condition_field"].(string)
	if !ok {
		return nil, fmt.Errorf("condition_field is required")
	}

	// Get condition operator
	operator, ok := config["operator"].(string)
	if !ok {
		operator = "equals"
	}

	// Get condition value
	conditionValue := config["condition_value"]

	// Get field value from input
	fieldValue, exists := input[conditionField]
	if !exists {
		return nil, fmt.Errorf("condition field %s not found in input", conditionField)
	}

	// Evaluate condition
	conditionMet := false
	switch operator {
	case "equals":
		conditionMet = fmt.Sprintf("%v", fieldValue) == fmt.Sprintf("%v", conditionValue)
	case "not_equals":
		conditionMet = fmt.Sprintf("%v", fieldValue) != fmt.Sprintf("%v", conditionValue)
	case "contains":
		fieldStr := fmt.Sprintf("%v", fieldValue)
		valueStr := fmt.Sprintf("%v", conditionValue)
		conditionMet = strings.Contains(fieldStr, valueStr)
	case "greater_than":
		if fieldNum, ok := fieldValue.(float64); ok {
			if valueNum, ok := conditionValue.(float64); ok {
				conditionMet = fieldNum > valueNum
			}
		}
	case "less_than":
		if fieldNum, ok := fieldValue.(float64); ok {
			if valueNum, ok := conditionValue.(float64); ok {
				conditionMet = fieldNum < valueNum
			}
		}
	default:
		return nil, fmt.Errorf("unsupported operator: %s", operator)
	}

	// Create output with routing information
	output := make(map[string]interface{})
	for k, v := range input {
		output[k] = v
	}
	output["condition_met"] = conditionMet
	output["route"] = map[string]interface{}{
		"condition_field": conditionField,
		"operator":        operator,
		"condition_value": conditionValue,
		"field_value":     fieldValue,
		"result":          conditionMet,
	}

	return output, nil
}

func (e *ConditionalRouterExecutor) GetType() NodeType {
	return NodeTypeConditionalRouter
}

// SchedulerExecutor executes scheduler nodes
type SchedulerExecutor struct{}

func (e *SchedulerExecutor) Execute(ctx context.Context, node *NodeSchema, input map[string]interface{}) (map[string]interface{}, error) {
	config, ok := node.Config.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid scheduler configuration")
	}

	// Get schedule type
	scheduleType, ok := config["schedule_type"].(string)
	if !ok {
		scheduleType = "immediate"
	}

	// Get delay or schedule time
	var nextRun time.Time
	switch scheduleType {
	case "immediate":
		nextRun = time.Now()
	case "delay":
		if delaySeconds, ok := config["delay_seconds"].(float64); ok {
			nextRun = time.Now().Add(time.Duration(delaySeconds) * time.Second)
		} else {
			nextRun = time.Now().Add(5 * time.Second) // Default 5 seconds
		}
	case "scheduled":
		if scheduleTime, ok := config["schedule_time"].(string); ok {
			if parsedTime, err := time.Parse(time.RFC3339, scheduleTime); err == nil {
				nextRun = parsedTime
			} else {
				return nil, fmt.Errorf("invalid schedule_time format: %s", scheduleTime)
			}
		} else {
			return nil, fmt.Errorf("schedule_time is required for scheduled type")
		}
	default:
		return nil, fmt.Errorf("unsupported schedule_type: %s", scheduleType)
	}

	// Create schedule result
	scheduleID := fmt.Sprintf("schedule_%d", time.Now().Unix())

	output := make(map[string]interface{})
	for k, v := range input {
		output[k] = v
	}
	output["schedule_id"] = scheduleID
	output["schedule_type"] = scheduleType
	output["next_run"] = nextRun.Format(time.RFC3339)
	output["scheduled_at"] = time.Now().Format(time.RFC3339)

	return output, nil
}

func (e *SchedulerExecutor) GetType() NodeType {
	return NodeTypeScheduler
}

// ErrorHandlerExecutor executes error handling nodes
type ErrorHandlerExecutor struct{}

func (e *ErrorHandlerExecutor) Execute(ctx context.Context, node *NodeSchema, input map[string]interface{}) (map[string]interface{}, error) {
	config, ok := node.Config.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid error handler configuration")
	}

	// Get error handling strategy
	strategy, ok := config["strategy"].(string)
	if !ok {
		strategy = "log"
	}

	// Check if there's an error in the input
	var errorMessage string
	var hasError bool

	if err, ok := input["error"].(string); ok && err != "" {
		errorMessage = err
		hasError = true
	} else if errObj, ok := input["error"].(error); ok && errObj != nil {
		errorMessage = errObj.Error()
		hasError = true
	}

	// Handle error based on strategy
	output := make(map[string]interface{})
	for k, v := range input {
		output[k] = v
	}

	if hasError {
		switch strategy {
		case "log":
			output["error_logged"] = true
			output["error_message"] = errorMessage
			output["logged_at"] = time.Now().Format(time.RFC3339)

		case "retry":
			retryCount := 0
			if count, ok := input["retry_count"].(float64); ok {
				retryCount = int(count)
			}
			maxRetries := 3
			if max, ok := config["max_retries"].(float64); ok {
				maxRetries = int(max)
			}

			if retryCount < maxRetries {
				output["should_retry"] = true
				output["retry_count"] = retryCount + 1
				output["retry_delay"] = 5 // seconds
			} else {
				output["should_retry"] = false
				output["max_retries_exceeded"] = true
			}

		case "fallback":
			fallbackValue := config["fallback_value"]
			if fallbackValue == nil {
				fallbackValue = "Error occurred, using fallback"
			}
			output["fallback_used"] = true
			output["fallback_value"] = fallbackValue
			output["original_error"] = errorMessage

		case "fail":
			return nil, fmt.Errorf("error handler configured to fail: %s", errorMessage)

		default:
			return nil, fmt.Errorf("unsupported error handling strategy: %s", strategy)
		}
	} else {
		output["no_error"] = true
	}

	output["error_handler_executed"] = true
	output["strategy_used"] = strategy

	return output, nil
}

func (e *ErrorHandlerExecutor) GetType() NodeType {
	return NodeTypeErrorHandler
}

// ImageProcessorExecutor executes image processing nodes
type ImageProcessorExecutor struct{}

func (e *ImageProcessorExecutor) Execute(ctx context.Context, node *NodeSchema, input map[string]interface{}) (map[string]interface{}, error) {
	config, ok := node.Config.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid image processor configuration")
	}

	// Get operation type
	operation, ok := config["operation"].(string)
	if !ok {
		return nil, fmt.Errorf("operation is required")
	}

	// Get image source
	imageURL, hasURL := input["image_url"].(string)
	imagePath, hasPath := input["image_path"].(string)

	if !hasURL && !hasPath {
		return nil, fmt.Errorf("image_url or image_path is required")
	}

	imageSource := imageURL
	if imageSource == "" {
		imageSource = imagePath
	}

	// Mock image processing based on operation
	result := map[string]interface{}{
		"success":      true,
		"operation":    operation,
		"source":       imageSource,
		"processed_at": time.Now().Format(time.RFC3339),
	}

	switch operation {
	case "resize":
		width := 800
		height := 600
		if w, ok := config["width"].(float64); ok {
			width = int(w)
		}
		if h, ok := config["height"].(float64); ok {
			height = int(h)
		}
		result["new_width"] = width
		result["new_height"] = height
		result["output_url"] = fmt.Sprintf("%s_resized_%dx%d.jpg", imageSource, width, height)

	case "crop":
		x := 0
		y := 0
		width := 400
		height := 300
		if xVal, ok := config["x"].(float64); ok {
			x = int(xVal)
		}
		if yVal, ok := config["y"].(float64); ok {
			y = int(yVal)
		}
		if w, ok := config["width"].(float64); ok {
			width = int(w)
		}
		if h, ok := config["height"].(float64); ok {
			height = int(h)
		}
		result["crop_x"] = x
		result["crop_y"] = y
		result["crop_width"] = width
		result["crop_height"] = height
		result["output_url"] = fmt.Sprintf("%s_cropped_%d_%d_%dx%d.jpg", imageSource, x, y, width, height)

	case "filter":
		filterType := "blur"
		if filter, ok := config["filter"].(string); ok {
			filterType = filter
		}
		result["filter_applied"] = filterType
		result["output_url"] = fmt.Sprintf("%s_filtered_%s.jpg", imageSource, filterType)

	case "analyze":
		result["analysis"] = map[string]interface{}{
			"width":      1920,
			"height":     1080,
			"format":     "JPEG",
			"size_bytes": 245760,
			"colors":     []string{"#FF0000", "#00FF00", "#0000FF"},
			"objects":    []string{"person", "car", "building"},
		}

	default:
		return nil, fmt.Errorf("unsupported image operation: %s", operation)
	}

	// Merge with input
	output := make(map[string]interface{})
	for k, v := range input {
		output[k] = v
	}
	for k, v := range result {
		output[k] = v
	}

	return output, nil
}

func (e *ImageProcessorExecutor) GetType() NodeType {
	return NodeTypeImageProcessor
}
