package workflow

import (
	"context"
	"fmt"
	"strings"
)

// EntryNodeExecutor executes entry nodes
type EntryNodeExecutor struct{}

func (e *EntryNodeExecutor) Execute(ctx context.Context, node *NodeSchema, input map[string]interface{}) (map[string]interface{}, error) {
	// Entry node simply passes through the input
	return input, nil
}

func (e *EntryNodeExecutor) GetType() NodeType {
	return NodeTypeEntry
}

// ExitNodeExecutor executes exit nodes
type ExitNodeExecutor struct{}

func (e *ExitNodeExecutor) Execute(ctx context.Context, node *NodeSchema, input map[string]interface{}) (map[string]interface{}, error) {
	// Exit node simply passes through the input as final output
	return input, nil
}

func (e *ExitNodeExecutor) GetType() NodeType {
	return NodeTypeExit
}

// SelectorNodeExecutor executes selector (conditional) nodes
type SelectorNodeExecutor struct{}

func (e *SelectorNodeExecutor) Execute(ctx context.Context, node *NodeSchema, input map[string]interface{}) (map[string]interface{}, error) {
	// Get selector configuration
	config, ok := node.Config.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid selector configuration")
	}

	conditions, ok := config["conditions"].([]interface{})
	if !ok {
		return nil, fmt.Errorf("no conditions defined for selector")
	}

	// Evaluate conditions
	for i, conditionData := range conditions {
		condition, ok := conditionData.(map[string]interface{})
		if !ok {
			continue
		}

		if e.evaluateCondition(condition, input) {
			// Return the branch index that matched
			return map[string]interface{}{
				"branch":    i,
				"condition": condition,
				"matched":   true,
			}, nil
		}
	}

	// No condition matched, return default
	return map[string]interface{}{
		"branch":  -1,
		"matched": false,
	}, nil
}

func (e *SelectorNodeExecutor) evaluateCondition(condition map[string]interface{}, input map[string]interface{}) bool {
	field, ok := condition["field"].(string)
	if !ok {
		return false
	}

	operator, ok := condition["operator"].(string)
	if !ok {
		return false
	}

	expectedValue := condition["value"]
	actualValue, exists := input[field]

	if !exists {
		return false
	}

	switch operator {
	case "equals":
		return actualValue == expectedValue
	case "not_equals":
		return actualValue != expectedValue
	case "contains":
		if actualStr, ok := actualValue.(string); ok {
			if expectedStr, ok := expectedValue.(string); ok {
				return strings.Contains(actualStr, expectedStr)
			}
		}
		return false
	case "greater_than":
		if actualNum, ok := actualValue.(float64); ok {
			if expectedNum, ok := expectedValue.(float64); ok {
				return actualNum > expectedNum
			}
		}
		return false
	case "less_than":
		if actualNum, ok := actualValue.(float64); ok {
			if expectedNum, ok := expectedValue.(float64); ok {
				return actualNum < expectedNum
			}
		}
		return false
	case "is_empty":
		if actualStr, ok := actualValue.(string); ok {
			return strings.TrimSpace(actualStr) == ""
		}
		return actualValue == nil
	case "is_not_empty":
		if actualStr, ok := actualValue.(string); ok {
			return strings.TrimSpace(actualStr) != ""
		}
		return actualValue != nil
	default:
		return false
	}
}

func (e *SelectorNodeExecutor) GetType() NodeType {
	return NodeTypeSelector
}

// VariableAssignerExecutor executes variable assignment nodes
type VariableAssignerExecutor struct{}

func (e *VariableAssignerExecutor) Execute(ctx context.Context, node *NodeSchema, input map[string]interface{}) (map[string]interface{}, error) {
	// Get variable assignment configuration
	config, ok := node.Config.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid variable assigner configuration")
	}

	assignments, ok := config["assignments"].([]interface{})
	if !ok {
		return nil, fmt.Errorf("no assignments defined")
	}

	output := make(map[string]interface{})

	// Copy input to output
	for k, v := range input {
		output[k] = v
	}

	// Process assignments
	for _, assignmentData := range assignments {
		assignment, ok := assignmentData.(map[string]interface{})
		if !ok {
			continue
		}

		variable, ok := assignment["variable"].(string)
		if !ok {
			continue
		}

		valueType, ok := assignment["type"].(string)
		if !ok {
			continue
		}

		switch valueType {
		case "constant":
			output[variable] = assignment["value"]
		case "reference":
			if sourceField, ok := assignment["source"].(string); ok {
				if value, exists := input[sourceField]; exists {
					output[variable] = value
				}
			}
		case "expression":
			// Simple expression evaluation (could be extended)
			if expr, ok := assignment["expression"].(string); ok {
				value, err := e.evaluateExpression(expr, input)
				if err == nil {
					output[variable] = value
				}
			}
		}
	}

	return output, nil
}

func (e *VariableAssignerExecutor) evaluateExpression(expr string, input map[string]interface{}) (interface{}, error) {
	// Simple expression evaluation - could be extended with a proper expression parser
	// For now, just support variable substitution
	for key, value := range input {
		placeholder := fmt.Sprintf("${%s}", key)
		if strings.Contains(expr, placeholder) {
			if strValue, ok := value.(string); ok {
				expr = strings.ReplaceAll(expr, placeholder, strValue)
			}
		}
	}
	return expr, nil
}

func (e *VariableAssignerExecutor) GetType() NodeType {
	return NodeTypeVariableAssigner
}

// TextProcessorExecutor executes text processing nodes
type TextProcessorExecutor struct{}

func (e *TextProcessorExecutor) Execute(ctx context.Context, node *NodeSchema, input map[string]interface{}) (map[string]interface{}, error) {
	// Get text processing configuration
	config, ok := node.Config.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid text processor configuration")
	}

	operation, ok := config["operation"].(string)
	if !ok {
		return nil, fmt.Errorf("no operation defined")
	}

	inputField, ok := config["input_field"].(string)
	if !ok {
		inputField = "text"
	}

	outputField, ok := config["output_field"].(string)
	if !ok {
		outputField = "result"
	}

	text, ok := input[inputField].(string)
	if !ok {
		return nil, fmt.Errorf("input field %s is not a string", inputField)
	}

	var result string

	switch operation {
	case "uppercase":
		result = strings.ToUpper(text)
	case "lowercase":
		result = strings.ToLower(text)
	case "trim":
		result = strings.TrimSpace(text)
	case "length":
		return map[string]interface{}{
			outputField: len(text),
		}, nil
	case "split":
		delimiter, ok := config["delimiter"].(string)
		if !ok {
			delimiter = ","
		}
		parts := strings.Split(text, delimiter)
		return map[string]interface{}{
			outputField: parts,
		}, nil
	case "replace":
		oldStr, ok := config["old"].(string)
		if !ok {
			return nil, fmt.Errorf("old string not specified for replace operation")
		}
		newStr, ok := config["new"].(string)
		if !ok {
			newStr = ""
		}
		result = strings.ReplaceAll(text, oldStr, newStr)
	default:
		return nil, fmt.Errorf("unsupported operation: %s", operation)
	}

	output := make(map[string]interface{})
	for k, v := range input {
		output[k] = v
	}
	output[outputField] = result

	return output, nil
}

func (e *TextProcessorExecutor) GetType() NodeType {
	return NodeTypeTextProcessor
}

// ContinueNodeExecutor executes continue nodes (no-op)
type ContinueNodeExecutor struct{}

func (e *ContinueNodeExecutor) Execute(ctx context.Context, node *NodeSchema, input map[string]interface{}) (map[string]interface{}, error) {
	// Continue node simply passes through the input
	return input, nil
}

func (e *ContinueNodeExecutor) GetType() NodeType {
	return NodeTypeContinue
}

// BreakNodeExecutor executes break nodes
type BreakNodeExecutor struct{}

func (e *BreakNodeExecutor) Execute(ctx context.Context, node *NodeSchema, input map[string]interface{}) (map[string]interface{}, error) {
	// Break node terminates execution
	return map[string]interface{}{
		"break": true,
	}, nil
}

func (e *BreakNodeExecutor) GetType() NodeType {
	return NodeTypeBreak
}

// HTTPRequesterExecutor executes HTTP request nodes (placeholder implementation)
type HTTPRequesterExecutor struct{}

func (e *HTTPRequesterExecutor) Execute(ctx context.Context, node *NodeSchema, input map[string]interface{}) (map[string]interface{}, error) {
	// Get HTTP configuration
	config, ok := node.Config.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid HTTP configuration")
	}

	url, ok := config["url"].(string)
	if !ok {
		return nil, fmt.Errorf("no URL defined")
	}

	method, ok := config["method"].(string)
	if !ok {
		method = "GET"
	}

	// Placeholder HTTP response (in real implementation, this would make an actual HTTP request)
	response := map[string]interface{}{
		"status_code": 200,
		"body":        fmt.Sprintf("Response from %s %s", method, url),
		"headers":     map[string]string{"Content-Type": "application/json"},
	}

	output := make(map[string]interface{})
	for k, v := range input {
		output[k] = v
	}
	output["http_response"] = response

	return output, nil
}

func (e *HTTPRequesterExecutor) GetType() NodeType {
	return NodeTypeHTTPRequester
}

// CodeRunnerExecutor executes code runner nodes (placeholder implementation)
type CodeRunnerExecutor struct{}

func (e *CodeRunnerExecutor) Execute(ctx context.Context, node *NodeSchema, input map[string]interface{}) (map[string]interface{}, error) {
	// Get code configuration
	config, ok := node.Config.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid code configuration")
	}

	code, ok := config["code"].(string)
	if !ok {
		return nil, fmt.Errorf("no code defined")
	}

	language, ok := config["language"].(string)
	if !ok {
		language = "javascript"
	}

	// Placeholder code execution (in real implementation, this would execute the code safely)
	result := fmt.Sprintf("Executed %s code: %s", language, code)

	output := make(map[string]interface{})
	for k, v := range input {
		output[k] = v
	}
	output["code_result"] = result
	output["language"] = language

	return output, nil
}

func (e *CodeRunnerExecutor) GetType() NodeType {
	return NodeTypeCodeRunner
}
