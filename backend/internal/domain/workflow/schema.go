package workflow

import (
	"encoding/json"
	"fmt"
	"time"
)

// WorkflowSchema represents the complete workflow definition
type WorkflowSchema struct {
	ID          string              `json:"id"`
	Name        string              `json:"name"`
	Description string              `json:"description,omitempty"`
	Version     string              `json:"version"`
	Status      WorkflowStatus      `json:"status"`
	Nodes       []*NodeSchema       `json:"nodes"`
	Connections []*Connection       `json:"connections"`
	Hierarchy   map[NodeKey]NodeKey `json:"hierarchy,omitempty"`
	CreatedAt   *time.Time          `json:"created_at,omitempty"`
	UpdatedAt   *time.Time          `json:"updated_at,omitempty"`

	// Internal fields (not serialized)
	nodeMap map[NodeKey]*NodeSchema `json:"-"`
}

// NodeSchema represents a single node in the workflow
type NodeSchema struct {
	Key         NodeKey   `json:"key"`
	Name        string    `json:"name"`
	Type        NodeType  `json:"type"`
	Description string    `json:"description,omitempty"`
	Position    *Position `json:"position,omitempty"`

	// Configuration
	Config        interface{}          `json:"config,omitempty"`
	InputTypes    map[string]*TypeInfo `json:"input_types,omitempty"`
	InputSources  []*FieldInfo         `json:"input_sources,omitempty"`
	OutputTypes   map[string]*TypeInfo `json:"output_types,omitempty"`
	OutputSources []*FieldInfo         `json:"output_sources,omitempty"`

	// Error handling and streaming
	ExceptionConfig *ExceptionConfig `json:"exception_config,omitempty"`
	StreamConfig    *StreamConfig    `json:"stream_config,omitempty"`
}

// Connection represents a connection between two nodes
type Connection struct {
	ID       string  `json:"id"`
	FromNode NodeKey `json:"from_node"`
	ToNode   NodeKey `json:"to_node"`
	FromPort string  `json:"from_port,omitempty"`
	ToPort   string  `json:"to_port,omitempty"`
}

// Position represents the visual position of a node
type Position struct {
	X float64 `json:"x"`
	Y float64 `json:"y"`
}

// Init initializes the workflow schema
func (w *WorkflowSchema) Init() {
	w.nodeMap = make(map[NodeKey]*NodeSchema)
	for _, node := range w.Nodes {
		w.nodeMap[node.Key] = node
	}
}

// GetNode returns a node by its key
func (w *WorkflowSchema) GetNode(key NodeKey) *NodeSchema {
	if w.nodeMap == nil {
		w.Init()
	}
	return w.nodeMap[key]
}

// GetAllNodes returns all nodes as a map
func (w *WorkflowSchema) GetAllNodes() map[NodeKey]*NodeSchema {
	if w.nodeMap == nil {
		w.Init()
	}
	return w.nodeMap
}

// AddNode adds a new node to the workflow
func (w *WorkflowSchema) AddNode(node *NodeSchema) error {
	if w.GetNode(node.Key) != nil {
		return fmt.Errorf("node with key %s already exists", node.Key)
	}

	w.Nodes = append(w.Nodes, node)
	if w.nodeMap != nil {
		w.nodeMap[node.Key] = node
	}
	return nil
}

// RemoveNode removes a node from the workflow
func (w *WorkflowSchema) RemoveNode(key NodeKey) error {
	// Remove from nodes slice
	for i, node := range w.Nodes {
		if node.Key == key {
			w.Nodes = append(w.Nodes[:i], w.Nodes[i+1:]...)
			break
		}
	}

	// Remove from node map
	if w.nodeMap != nil {
		delete(w.nodeMap, key)
	}

	// Remove related connections
	var newConnections []*Connection
	for _, conn := range w.Connections {
		if conn.FromNode != key && conn.ToNode != key {
			newConnections = append(newConnections, conn)
		}
	}
	w.Connections = newConnections

	return nil
}

// AddConnection adds a new connection between nodes
func (w *WorkflowSchema) AddConnection(conn *Connection) error {
	// Validate that both nodes exist
	if w.GetNode(conn.FromNode) == nil {
		return fmt.Errorf("from node %s does not exist", conn.FromNode)
	}
	if w.GetNode(conn.ToNode) == nil {
		return fmt.Errorf("to node %s does not exist", conn.ToNode)
	}

	w.Connections = append(w.Connections, conn)
	return nil
}

// RemoveConnection removes a connection by ID
func (w *WorkflowSchema) RemoveConnection(id string) error {
	for i, conn := range w.Connections {
		if conn.ID == id {
			w.Connections = append(w.Connections[:i], w.Connections[i+1:]...)
			return nil
		}
	}
	return fmt.Errorf("connection with id %s not found", id)
}

// Validate validates the workflow schema
func (w *WorkflowSchema) Validate() error {
	if len(w.Nodes) == 0 {
		return fmt.Errorf("workflow must have at least one node")
	}

	// Check for entry and exit nodes
	hasEntry := false
	hasExit := false
	for _, node := range w.Nodes {
		if node.Type == NodeTypeEntry {
			hasEntry = true
		}
		if node.Type == NodeTypeExit {
			hasExit = true
		}
	}

	if !hasEntry {
		return fmt.Errorf("workflow must have an entry node")
	}
	if !hasExit {
		return fmt.Errorf("workflow must have an exit node")
	}

	// Validate connections
	for _, conn := range w.Connections {
		if w.GetNode(conn.FromNode) == nil {
			return fmt.Errorf("connection references non-existent from node: %s", conn.FromNode)
		}
		if w.GetNode(conn.ToNode) == nil {
			return fmt.Errorf("connection references non-existent to node: %s", conn.ToNode)
		}
	}

	return nil
}

// Clone creates a deep copy of the workflow schema
func (w *WorkflowSchema) Clone() (*WorkflowSchema, error) {
	data, err := json.Marshal(w)
	if err != nil {
		return nil, err
	}

	var clone WorkflowSchema
	if err := json.Unmarshal(data, &clone); err != nil {
		return nil, err
	}

	clone.Init()
	return &clone, nil
}
