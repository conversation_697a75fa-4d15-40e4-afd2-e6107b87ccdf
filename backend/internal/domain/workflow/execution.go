package workflow

import (
	"time"
)

// WorkflowExecution represents a workflow execution instance
type WorkflowExecution struct {
	ID         string          `json:"id"`
	WorkflowID string          `json:"workflow_id"`
	Version    string          `json:"version"`
	Status     ExecutionStatus `json:"status"`
	Input      interface{}     `json:"input,omitempty"`
	Output     interface{}     `json:"output,omitempty"`
	Error      string          `json:"error,omitempty"`
	
	// Timing information
	StartedAt   time.Time  `json:"started_at"`
	CompletedAt *time.Time `json:"completed_at,omitempty"`
	Duration    int64      `json:"duration,omitempty"` // milliseconds
	
	// Resource usage
	TokenUsage *TokenUsage `json:"token_usage,omitempty"`
	
	// Node executions
	NodeExecutions []*NodeExecution `json:"node_executions,omitempty"`
	
	// Execution context
	Context map[string]interface{} `json:"context,omitempty"`
}

// NodeExecution represents the execution of a single node
type NodeExecution struct {
	ID       string              `json:"id"`
	NodeKey  <PERSON>             `json:"node_key"`
	NodeName string              `json:"node_name"`
	NodeType NodeType            `json:"node_type"`
	Status   NodeExecutionStatus `json:"status"`
	
	// Input/Output data
	Input     interface{} `json:"input,omitempty"`
	Output    interface{} `json:"output,omitempty"`
	RawOutput interface{} `json:"raw_output,omitempty"`
	Error     string      `json:"error,omitempty"`
	
	// Timing information
	StartedAt   time.Time  `json:"started_at"`
	CompletedAt *time.Time `json:"completed_at,omitempty"`
	Duration    int64      `json:"duration,omitempty"` // milliseconds
	
	// Resource usage
	TokenUsage *TokenUsage `json:"token_usage,omitempty"`
	
	// Retry information
	RetryCount int `json:"retry_count,omitempty"`
	
	// Additional metadata
	Metadata map[string]interface{} `json:"metadata,omitempty"`
}

// ExecutionEvent represents an event during workflow execution
type ExecutionEvent struct {
	ID          string                 `json:"id"`
	ExecutionID string                 `json:"execution_id"`
	Type        ExecutionEventType     `json:"type"`
	Timestamp   time.Time              `json:"timestamp"`
	Data        map[string]interface{} `json:"data,omitempty"`
}

// ExecutionEventType represents the type of execution event
type ExecutionEventType string

const (
	EventTypeWorkflowStarted   ExecutionEventType = "workflow_started"
	EventTypeWorkflowCompleted ExecutionEventType = "workflow_completed"
	EventTypeWorkflowFailed    ExecutionEventType = "workflow_failed"
	EventTypeWorkflowCancelled ExecutionEventType = "workflow_cancelled"
	EventTypeNodeStarted       ExecutionEventType = "node_started"
	EventTypeNodeCompleted     ExecutionEventType = "node_completed"
	EventTypeNodeFailed        ExecutionEventType = "node_failed"
	EventTypeNodeSkipped       ExecutionEventType = "node_skipped"
	EventTypeStreamOutput      ExecutionEventType = "stream_output"
)

// ExecutionContext holds the runtime context for workflow execution
type ExecutionContext struct {
	ExecutionID string                 `json:"execution_id"`
	WorkflowID  string                 `json:"workflow_id"`
	Variables   map[string]interface{} `json:"variables"`
	NodeStates  map[NodeKey]*NodeState `json:"node_states"`
	
	// Execution options
	Options *ExecutionOptions `json:"options,omitempty"`
}

// NodeState represents the runtime state of a node
type NodeState struct {
	NodeKey   NodeKey             `json:"node_key"`
	Status    NodeExecutionStatus `json:"status"`
	Input     interface{}         `json:"input,omitempty"`
	Output    interface{}         `json:"output,omitempty"`
	Error     string              `json:"error,omitempty"`
	StartedAt time.Time           `json:"started_at"`
	UpdatedAt time.Time           `json:"updated_at"`
}

// ExecutionOptions represents options for workflow execution
type ExecutionOptions struct {
	// Execution mode
	Async bool `json:"async,omitempty"`
	
	// Timeout settings
	TimeoutSeconds int64 `json:"timeout_seconds,omitempty"`
	
	// Retry settings
	MaxRetries int `json:"max_retries,omitempty"`
	
	// Streaming settings
	EnableStreaming bool `json:"enable_streaming,omitempty"`
	
	// Debug settings
	EnableDebug bool `json:"enable_debug,omitempty"`
	
	// Custom context
	Context map[string]interface{} `json:"context,omitempty"`
}

// IsCompleted returns true if the execution is in a terminal state
func (e *WorkflowExecution) IsCompleted() bool {
	return e.Status == ExecutionStatusSuccess ||
		e.Status == ExecutionStatusFailed ||
		e.Status == ExecutionStatusCancelled
}

// IsRunning returns true if the execution is currently running
func (e *WorkflowExecution) IsRunning() bool {
	return e.Status == ExecutionStatusRunning
}

// GetDuration returns the execution duration in milliseconds
func (e *WorkflowExecution) GetDuration() int64 {
	if e.CompletedAt != nil {
		return e.CompletedAt.Sub(e.StartedAt).Milliseconds()
	}
	if e.IsRunning() {
		return time.Since(e.StartedAt).Milliseconds()
	}
	return e.Duration
}

// AddNodeExecution adds a node execution to the workflow execution
func (e *WorkflowExecution) AddNodeExecution(nodeExec *NodeExecution) {
	e.NodeExecutions = append(e.NodeExecutions, nodeExec)
}

// GetNodeExecution returns the node execution for a given node key
func (e *WorkflowExecution) GetNodeExecution(nodeKey NodeKey) *NodeExecution {
	for _, nodeExec := range e.NodeExecutions {
		if nodeExec.NodeKey == nodeKey {
			return nodeExec
		}
	}
	return nil
}

// Complete marks the execution as completed
func (e *WorkflowExecution) Complete(status ExecutionStatus, output interface{}, err string) {
	now := time.Now()
	e.Status = status
	e.Output = output
	e.Error = err
	e.CompletedAt = &now
	e.Duration = e.GetDuration()
}

// IsCompleted returns true if the node execution is in a terminal state
func (n *NodeExecution) IsCompleted() bool {
	return n.Status == NodeExecutionStatusSuccess ||
		n.Status == NodeExecutionStatusFailed ||
		n.Status == NodeExecutionStatusSkipped
}

// Complete marks the node execution as completed
func (n *NodeExecution) Complete(status NodeExecutionStatus, output interface{}, err string) {
	now := time.Now()
	n.Status = status
	n.Output = output
	n.Error = err
	n.CompletedAt = &now
	n.Duration = n.CompletedAt.Sub(n.StartedAt).Milliseconds()
}
