package executors

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"text/template"
	"time"

	"workflow-engine/internal/domain/workflow"
)

// LLMExecutor handles LLM node execution
type LLMExecutor struct {
	httpClient *http.Client
	apiKeys    map[string]string
}

// NewLLMExecutor creates a new LLM executor
func NewLLMExecutor() *LLMExecutor {
	return &LLMExecutor{
		httpClient: &http.Client{
			Timeout: 60 * time.Second,
		},
		apiKeys: make(map[string]string),
	}
}

// SetAPIKey sets an API key for a specific provider
func (e *LLMExecutor) SetAPIKey(provider, key string) {
	e.apiKeys[provider] = key
}

// ExecuteLLM executes an LLM node
func (e *LLMExecutor) ExecuteLLM(ctx context.Context, node *workflow.NodeSchema, input map[string]interface{}) (map[string]interface{}, error) {
	// Convert config to map[string]interface{}
	configMap, ok := node.Config.(map[string]interface{})
	if !ok {
		configMap = make(map[string]interface{})
	}

	// Get model
	var model string
	if modelVal, exists := configMap["model"]; exists {
		if modelStr, ok := modelVal.(string); ok {
			model = modelStr
		}
	}
	if model == "" {
		model = "gpt-4o-mini"
	}

	// Build messages
	messages, err := e.buildMessages(configMap, input)
	if err != nil {
		return nil, fmt.Errorf("failed to build messages: %w", err)
	}

	// Get provider from model
	provider := e.getProviderFromModel(model)

	// Execute based on provider
	switch provider {
	case "openai":
		return e.executeOpenAI(ctx, model, messages, configMap)
	case "anthropic":
		return e.executeAnthropic(ctx, model, messages, configMap)
	case "google":
		return e.executeGoogle(ctx, model, messages, configMap)
	default:
		return nil, fmt.Errorf("unsupported model provider: %s", provider)
	}
}

// buildMessages builds the messages array for LLM request
func (e *LLMExecutor) buildMessages(config map[string]interface{}, input map[string]interface{}) ([]map[string]interface{}, error) {
	var messages []map[string]interface{}

	// Add system message if provided
	if systemPrompt, ok := config["system_prompt"].(string); ok && systemPrompt != "" {
		messages = append(messages, map[string]interface{}{
			"role":    "system",
			"content": systemPrompt,
		})
	}

	// Process user prompt template
	userPrompt := "{{user_input}}"
	if prompt, ok := config["user_prompt"].(string); ok && prompt != "" {
		userPrompt = prompt
	}

	// Render user prompt with variables
	renderedPrompt, err := e.renderTemplate(userPrompt, input)
	if err != nil {
		return nil, fmt.Errorf("failed to render user prompt: %w", err)
	}

	// Add existing messages if provided
	if existingMessages, ok := input["messages"].([]interface{}); ok {
		for _, msg := range existingMessages {
			if msgMap, ok := msg.(map[string]interface{}); ok {
				messages = append(messages, msgMap)
			}
		}
	}

	// Add current user message
	if renderedPrompt != "" {
		messages = append(messages, map[string]interface{}{
			"role":    "user",
			"content": renderedPrompt,
		})
	}

	return messages, nil
}

// renderTemplate renders a template with input variables
func (e *LLMExecutor) renderTemplate(templateStr string, data map[string]interface{}) (string, error) {
	tmpl, err := template.New("prompt").Parse(templateStr)
	if err != nil {
		return "", err
	}

	var buf bytes.Buffer
	if err := tmpl.Execute(&buf, data); err != nil {
		return "", err
	}

	return buf.String(), nil
}

// executeOpenAI executes OpenAI API request
func (e *LLMExecutor) executeOpenAI(ctx context.Context, model string, messages []map[string]interface{}, config map[string]interface{}) (map[string]interface{}, error) {
	apiKey, ok := e.apiKeys["openai"]
	if !ok {
		return nil, fmt.Errorf("OpenAI API key not configured")
	}

	// Build request payload
	payload := map[string]interface{}{
		"model":    model,
		"messages": messages,
	}

	// Add optional parameters
	if temp, ok := config["temperature"].(float64); ok {
		payload["temperature"] = temp
	}
	if maxTokens, ok := config["max_tokens"].(float64); ok {
		payload["max_tokens"] = int(maxTokens)
	}
	if topP, ok := config["top_p"].(float64); ok {
		payload["top_p"] = topP
	}
	if stream, ok := config["stream"].(bool); ok {
		payload["stream"] = stream
	}

	// Make request
	response, err := e.makeHTTPRequest(ctx, "https://api.openai.com/v1/chat/completions", payload, map[string]string{
		"Authorization": "Bearer " + apiKey,
		"Content-Type":  "application/json",
	})
	if err != nil {
		return nil, err
	}

	// Parse response
	var result map[string]interface{}
	if err := json.Unmarshal(response, &result); err != nil {
		return nil, fmt.Errorf("failed to parse OpenAI response: %w", err)
	}

	// Extract response content
	choices, ok := result["choices"].([]interface{})
	if !ok || len(choices) == 0 {
		return nil, fmt.Errorf("no choices in OpenAI response")
	}

	choice := choices[0].(map[string]interface{})
	message := choice["message"].(map[string]interface{})
	content := message["content"].(string)

	// Update messages array
	updatedMessages := append(messages, map[string]interface{}{
		"role":    "assistant",
		"content": content,
	})

	// Extract usage information
	var tokensUsed float64
	if usage, ok := result["usage"].(map[string]interface{}); ok {
		if totalTokens, ok := usage["total_tokens"].(float64); ok {
			tokensUsed = totalTokens
		}
	}

	return map[string]interface{}{
		"response":      content,
		"messages":      updatedMessages,
		"tokens_used":   tokensUsed,
		"finish_reason": choice["finish_reason"],
		"model_used":    model,
	}, nil
}

// executeAnthropic executes Anthropic Claude API request
func (e *LLMExecutor) executeAnthropic(ctx context.Context, model string, messages []map[string]interface{}, config map[string]interface{}) (map[string]interface{}, error) {
	apiKey, ok := e.apiKeys["anthropic"]
	if !ok {
		return nil, fmt.Errorf("Anthropic API key not configured")
	}

	// Convert messages format for Claude
	var systemMessage string
	var claudeMessages []map[string]interface{}

	for _, msg := range messages {
		role := msg["role"].(string)
		content := msg["content"].(string)

		if role == "system" {
			systemMessage = content
		} else {
			claudeMessages = append(claudeMessages, map[string]interface{}{
				"role":    role,
				"content": content,
			})
		}
	}

	// Build request payload
	payload := map[string]interface{}{
		"model":      model,
		"messages":   claudeMessages,
		"max_tokens": 1000,
	}

	if systemMessage != "" {
		payload["system"] = systemMessage
	}

	// Add optional parameters
	if temp, ok := config["temperature"].(float64); ok {
		payload["temperature"] = temp
	}
	if maxTokens, ok := config["max_tokens"].(float64); ok {
		payload["max_tokens"] = int(maxTokens)
	}

	// Make request
	response, err := e.makeHTTPRequest(ctx, "https://api.anthropic.com/v1/messages", payload, map[string]string{
		"x-api-key":         apiKey,
		"Content-Type":      "application/json",
		"anthropic-version": "2023-06-01",
	})
	if err != nil {
		return nil, err
	}

	// Parse response
	var result map[string]interface{}
	if err := json.Unmarshal(response, &result); err != nil {
		return nil, fmt.Errorf("failed to parse Anthropic response: %w", err)
	}

	// Extract response content
	content, ok := result["content"].([]interface{})
	if !ok || len(content) == 0 {
		return nil, fmt.Errorf("no content in Anthropic response")
	}

	textContent := content[0].(map[string]interface{})["text"].(string)

	// Update messages array
	updatedMessages := append(messages, map[string]interface{}{
		"role":    "assistant",
		"content": textContent,
	})

	// Extract usage information
	var tokensUsed float64
	if usage, ok := result["usage"].(map[string]interface{}); ok {
		if inputTokens, ok := usage["input_tokens"].(float64); ok {
			tokensUsed += inputTokens
		}
		if outputTokens, ok := usage["output_tokens"].(float64); ok {
			tokensUsed += outputTokens
		}
	}

	return map[string]interface{}{
		"response":      textContent,
		"messages":      updatedMessages,
		"tokens_used":   tokensUsed,
		"finish_reason": result["stop_reason"],
		"model_used":    model,
	}, nil
}

// executeGoogle executes Google Gemini API request
func (e *LLMExecutor) executeGoogle(ctx context.Context, model string, messages []map[string]interface{}, config map[string]interface{}) (map[string]interface{}, error) {
	// Placeholder for Google Gemini implementation
	return nil, fmt.Errorf("Google Gemini integration not yet implemented")
}

// getProviderFromModel determines the provider from model name
func (e *LLMExecutor) getProviderFromModel(model string) string {
	if strings.HasPrefix(model, "gpt-") {
		return "openai"
	}
	if strings.HasPrefix(model, "claude-") {
		return "anthropic"
	}
	if strings.HasPrefix(model, "gemini-") {
		return "google"
	}
	return "openai" // default
}

// makeHTTPRequest makes an HTTP request with the given payload and headers
func (e *LLMExecutor) makeHTTPRequest(ctx context.Context, url string, payload map[string]interface{}, headers map[string]string) ([]byte, error) {
	jsonData, err := json.Marshal(payload)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal payload: %w", err)
	}

	req, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	for key, value := range headers {
		req.Header.Set(key, value)
	}

	resp, err := e.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to make request: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API request failed with status %d: %s", resp.StatusCode, string(body))
	}

	return body, nil
}
