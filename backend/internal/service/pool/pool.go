package pool

import (
	"context"
	"fmt"
	"net/http"
	"sync"
	"time"
)

// Config holds connection pool configuration
type Config struct {
	MaxConnections    int           `json:"max_connections"`
	MaxIdleTime       time.Duration `json:"max_idle_time"`
	ConnectionTimeout time.Duration `json:"connection_timeout"`
	KeepAlive         time.Duration `json:"keep_alive"`
	MaxIdleConns      int           `json:"max_idle_conns"`
	MaxIdleConnsPerHost int         `json:"max_idle_conns_per_host"`
}

// DefaultConfig returns a default connection pool configuration
func DefaultConfig() *Config {
	return &Config{
		MaxConnections:      100,
		MaxIdleTime:         90 * time.Second,
		ConnectionTimeout:   30 * time.Second,
		KeepAlive:          30 * time.Second,
		MaxIdleConns:       100,
		MaxIdleConnsPerHost: 10,
	}
}

// HTTPClientPool manages HTTP client connections
type HTTPClientPool struct {
	config  *Config
	clients map[string]*http.Client
	mutex   sync.RWMutex
}

// NewHTTPClientPool creates a new HTTP client pool
func NewHTTPClientPool(config *Config) *HTTPClientPool {
	if config == nil {
		config = DefaultConfig()
	}
	
	return &HTTPClientPool{
		config:  config,
		clients: make(map[string]*http.Client),
	}
}

// GetClient returns an HTTP client for the given key
func (p *HTTPClientPool) GetClient(key string) *http.Client {
	p.mutex.RLock()
	client, exists := p.clients[key]
	p.mutex.RUnlock()
	
	if exists {
		return client
	}
	
	p.mutex.Lock()
	defer p.mutex.Unlock()
	
	// Double-check after acquiring write lock
	if client, exists := p.clients[key]; exists {
		return client
	}
	
	// Create new client
	client = p.createClient()
	p.clients[key] = client
	
	return client
}

// createClient creates a new HTTP client with optimized settings
func (p *HTTPClientPool) createClient() *http.Client {
	transport := &http.Transport{
		MaxIdleConns:        p.config.MaxIdleConns,
		MaxIdleConnsPerHost: p.config.MaxIdleConnsPerHost,
		IdleConnTimeout:     p.config.MaxIdleTime,
		DisableCompression:  false,
		ForceAttemptHTTP2:   true,
	}
	
	return &http.Client{
		Transport: transport,
		Timeout:   p.config.ConnectionTimeout,
	}
}

// CleanupIdleConnections removes idle connections
func (p *HTTPClientPool) CleanupIdleConnections() {
	p.mutex.Lock()
	defer p.mutex.Unlock()
	
	for key, client := range p.clients {
		if transport, ok := client.Transport.(*http.Transport); ok {
			transport.CloseIdleConnections()
		}
		delete(p.clients, key)
	}
}

// GetStats returns pool statistics
func (p *HTTPClientPool) GetStats() map[string]interface{} {
	p.mutex.RLock()
	defer p.mutex.RUnlock()
	
	return map[string]interface{}{
		"total_clients":     len(p.clients),
		"max_connections":   p.config.MaxConnections,
		"max_idle_time":     p.config.MaxIdleTime.String(),
		"connection_timeout": p.config.ConnectionTimeout.String(),
	}
}

// WorkerPool manages a pool of workers for parallel execution
type WorkerPool struct {
	workerCount int
	jobQueue    chan Job
	workers     []*Worker
	wg          sync.WaitGroup
	ctx         context.Context
	cancel      context.CancelFunc
}

// Job represents a unit of work
type Job struct {
	ID       string
	Function func() (interface{}, error)
	Result   chan JobResult
}

// JobResult represents the result of a job
type JobResult struct {
	ID     string
	Result interface{}
	Error  error
}

// Worker represents a worker in the pool
type Worker struct {
	id       int
	jobQueue chan Job
	quit     chan bool
}

// NewWorkerPool creates a new worker pool
func NewWorkerPool(workerCount int) *WorkerPool {
	ctx, cancel := context.WithCancel(context.Background())
	
	pool := &WorkerPool{
		workerCount: workerCount,
		jobQueue:    make(chan Job, workerCount*2), // Buffer for jobs
		workers:     make([]*Worker, workerCount),
		ctx:         ctx,
		cancel:      cancel,
	}
	
	// Create and start workers
	for i := 0; i < workerCount; i++ {
		worker := &Worker{
			id:       i,
			jobQueue: pool.jobQueue,
			quit:     make(chan bool),
		}
		pool.workers[i] = worker
		pool.wg.Add(1)
		go worker.start(&pool.wg, pool.ctx)
	}
	
	return pool
}

// Submit submits a job to the worker pool
func (p *WorkerPool) Submit(id string, fn func() (interface{}, error)) <-chan JobResult {
	resultChan := make(chan JobResult, 1)
	
	job := Job{
		ID:       id,
		Function: fn,
		Result:   resultChan,
	}
	
	select {
	case p.jobQueue <- job:
		return resultChan
	case <-p.ctx.Done():
		// Pool is shutting down
		resultChan <- JobResult{
			ID:    id,
			Error: fmt.Errorf("worker pool is shutting down"),
		}
		return resultChan
	}
}

// SubmitBatch submits multiple jobs and returns when all are complete
func (p *WorkerPool) SubmitBatch(jobs map[string]func() (interface{}, error)) map[string]JobResult {
	results := make(map[string]JobResult)
	resultChans := make(map[string]<-chan JobResult)
	
	// Submit all jobs
	for id, fn := range jobs {
		resultChans[id] = p.Submit(id, fn)
	}
	
	// Collect all results
	for id, resultChan := range resultChans {
		results[id] = <-resultChan
	}
	
	return results
}

// Shutdown gracefully shuts down the worker pool
func (p *WorkerPool) Shutdown() {
	p.cancel()
	
	// Close job queue
	close(p.jobQueue)
	
	// Wait for all workers to finish
	p.wg.Wait()
}

// GetStats returns worker pool statistics
func (p *WorkerPool) GetStats() map[string]interface{} {
	return map[string]interface{}{
		"worker_count":    p.workerCount,
		"queue_length":    len(p.jobQueue),
		"queue_capacity":  cap(p.jobQueue),
	}
}

// start starts the worker
func (w *Worker) start(wg *sync.WaitGroup, ctx context.Context) {
	defer wg.Done()
	
	for {
		select {
		case job := <-w.jobQueue:
			// Execute the job
			result, err := job.Function()
			
			// Send result back
			job.Result <- JobResult{
				ID:     job.ID,
				Result: result,
				Error:  err,
			}
			close(job.Result)
			
		case <-ctx.Done():
			// Context cancelled, shutdown worker
			return
		}
	}
}

// CachePool manages a pool of cache instances
type CachePool struct {
	caches map[string]*Cache
	mutex  sync.RWMutex
	config *CacheConfig
}

// CacheConfig holds cache configuration
type CacheConfig struct {
	DefaultTTL    time.Duration `json:"default_ttl"`
	CleanupInterval time.Duration `json:"cleanup_interval"`
	MaxSize       int           `json:"max_size"`
}

// Cache represents a simple in-memory cache
type Cache struct {
	data    map[string]*CacheEntry
	mutex   sync.RWMutex
	config  *CacheConfig
	lastCleanup time.Time
}

// CacheEntry represents a cache entry
type CacheEntry struct {
	Value     interface{}
	ExpiresAt time.Time
}

// NewCachePool creates a new cache pool
func NewCachePool(config *CacheConfig) *CachePool {
	if config == nil {
		config = &CacheConfig{
			DefaultTTL:      5 * time.Minute,
			CleanupInterval: 1 * time.Minute,
			MaxSize:         1000,
		}
	}
	
	return &CachePool{
		caches: make(map[string]*Cache),
		config: config,
	}
}

// GetCache returns a cache instance for the given key
func (p *CachePool) GetCache(key string) *Cache {
	p.mutex.RLock()
	cache, exists := p.caches[key]
	p.mutex.RUnlock()
	
	if exists {
		return cache
	}
	
	p.mutex.Lock()
	defer p.mutex.Unlock()
	
	// Double-check after acquiring write lock
	if cache, exists := p.caches[key]; exists {
		return cache
	}
	
	// Create new cache
	cache = &Cache{
		data:        make(map[string]*CacheEntry),
		config:      p.config,
		lastCleanup: time.Now(),
	}
	p.caches[key] = cache
	
	return cache
}

// Set stores a value in the cache
func (c *Cache) Set(key string, value interface{}, ttl time.Duration) {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	
	if ttl == 0 {
		ttl = c.config.DefaultTTL
	}
	
	c.data[key] = &CacheEntry{
		Value:     value,
		ExpiresAt: time.Now().Add(ttl),
	}
	
	// Cleanup if needed
	if time.Since(c.lastCleanup) > c.config.CleanupInterval {
		c.cleanupExpired()
	}
}

// Get retrieves a value from the cache
func (c *Cache) Get(key string) (interface{}, bool) {
	c.mutex.RLock()
	defer c.mutex.RUnlock()
	
	entry, exists := c.data[key]
	if !exists {
		return nil, false
	}
	
	if time.Now().After(entry.ExpiresAt) {
		return nil, false
	}
	
	return entry.Value, true
}

// Delete removes a value from the cache
func (c *Cache) Delete(key string) {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	
	delete(c.data, key)
}

// cleanupExpired removes expired entries (must be called with write lock)
func (c *Cache) cleanupExpired() {
	now := time.Now()
	for key, entry := range c.data {
		if now.After(entry.ExpiresAt) {
			delete(c.data, key)
		}
	}
	c.lastCleanup = now
}

// GetStats returns cache statistics
func (c *Cache) GetStats() map[string]interface{} {
	c.mutex.RLock()
	defer c.mutex.RUnlock()
	
	expired := 0
	now := time.Now()
	for _, entry := range c.data {
		if now.After(entry.ExpiresAt) {
			expired++
		}
	}
	
	return map[string]interface{}{
		"total_entries":   len(c.data),
		"expired_entries": expired,
		"valid_entries":   len(c.data) - expired,
		"max_size":        c.config.MaxSize,
	}
}
