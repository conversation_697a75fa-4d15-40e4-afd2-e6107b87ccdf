package optimization

import (
	"context"
	"time"
)

// ParallelExecutor defines the interface for parallel execution
type ParallelExecutor interface {
	ExecuteNodes(ctx context.Context, executions []NodeExecution) (map[string]ExecutionResult, error)
	ExecuteBatch(ctx context.Context, batchType string, operations []func() (interface{}, error)) ([]interface{}, error)
	GetStats() map[string]interface{}
	Shutdown()
}

// NodeExecution represents a node execution task
type NodeExecution struct {
	NodeKey  string
	Input    map[string]interface{}
	Execute  func(ctx context.Context, input map[string]interface{}) (map[string]interface{}, error)
	Context  context.Context
}

// ExecutionResult represents the result of a node execution
type ExecutionResult struct {
	NodeKey  string
	Output   map[string]interface{}
	Error    error
	Duration time.Duration
}

// CacheManager defines the interface for caching
type CacheManager interface {
	Get(key string) (interface{}, bool)
	Set(key string, value interface{}, ttl time.Duration)
	Delete(key string)
	GetStats() map[string]interface{}
}

// ConnectionPool defines the interface for connection pooling
type ConnectionPool interface {
	GetConnection(key string) interface{}
	ReleaseConnection(key string, conn interface{})
	GetStats() map[string]interface{}
	Cleanup()
}

// PerformanceMonitor defines the interface for performance monitoring
type PerformanceMonitor interface {
	RecordExecution(nodeType string, duration time.Duration, success bool)
	GetMetrics() map[string]interface{}
	GetRecommendations() []string
}

// OptimizationConfig holds optimization configuration
type OptimizationConfig struct {
	EnableParallelExecution bool          `json:"enable_parallel_execution"`
	MaxConcurrency         int           `json:"max_concurrency"`
	EnableCaching          bool          `json:"enable_caching"`
	CacheTTL               time.Duration `json:"cache_ttl"`
	EnableConnectionPooling bool         `json:"enable_connection_pooling"`
	PoolSize               int           `json:"pool_size"`
}

// DefaultOptimizationConfig returns default optimization configuration
func DefaultOptimizationConfig() *OptimizationConfig {
	return &OptimizationConfig{
		EnableParallelExecution: true,
		MaxConcurrency:         10,
		EnableCaching:          true,
		CacheTTL:               5 * time.Minute,
		EnableConnectionPooling: true,
		PoolSize:               20,
	}
}
