package llm

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"

	"workflow-engine/internal/config"
)

// AnthropicClient handles Anthropic Claude API interactions
type AnthropicClient struct {
	apiKey     string
	baseURL    string
	httpClient *http.Client
}

// NewAnthropicClient creates a new Anthropic client
func NewAnthropicClient(cfg config.AnthropicConfig) *AnthropicClient {
	return &AnthropicClient{
		apiKey:  cfg.APIKey,
		baseURL: cfg.BaseURL,
		httpClient: &http.Client{
			Timeout: cfg.Timeout,
		},
	}
}

// AnthropicMessage represents a message in Claude format
type AnthropicMessage struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

// AnthropicRequest represents a Claude API request
type AnthropicRequest struct {
	Model     string             `json:"model"`
	MaxTokens int                `json:"max_tokens"`
	Messages  []AnthropicMessage `json:"messages"`
	System    string             `json:"system,omitempty"`
	Temperature *float64         `json:"temperature,omitempty"`
	TopP        *float64         `json:"top_p,omitempty"`
	TopK        *int             `json:"top_k,omitempty"`
	Stream      bool             `json:"stream,omitempty"`
}

// AnthropicResponse represents a Claude API response
type AnthropicResponse struct {
	ID           string                   `json:"id"`
	Type         string                   `json:"type"`
	Role         string                   `json:"role"`
	Content      []AnthropicContentBlock  `json:"content"`
	Model        string                   `json:"model"`
	StopReason   string                   `json:"stop_reason"`
	StopSequence string                   `json:"stop_sequence"`
	Usage        AnthropicUsage           `json:"usage"`
}

// AnthropicContentBlock represents a content block in the response
type AnthropicContentBlock struct {
	Type string `json:"type"`
	Text string `json:"text"`
}

// AnthropicUsage represents token usage information
type AnthropicUsage struct {
	InputTokens  int `json:"input_tokens"`
	OutputTokens int `json:"output_tokens"`
}

// CreateMessage sends a message to Claude
func (c *AnthropicClient) CreateMessage(ctx context.Context, req AnthropicRequest) (*AnthropicResponse, error) {
	if c.apiKey == "" {
		return nil, fmt.Errorf("Anthropic API key not configured")
	}

	// Marshal request
	jsonData, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	// Create HTTP request
	httpReq, err := http.NewRequestWithContext(ctx, "POST", c.baseURL+"/v1/messages", bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// Set headers
	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("x-api-key", c.apiKey)
	httpReq.Header.Set("anthropic-version", "2023-06-01")

	// Send request
	resp, err := c.httpClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	// Read response
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	// Check status code
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API request failed with status %d: %s", resp.StatusCode, string(body))
	}

	// Parse response
	var response AnthropicResponse
	if err := json.Unmarshal(body, &response); err != nil {
		return nil, fmt.Errorf("failed to parse response: %w", err)
	}

	return &response, nil
}

// IsConfigured returns true if the client is properly configured
func (c *AnthropicClient) IsConfigured() bool {
	return c.apiKey != ""
}

// GetModels returns available Claude models
func (c *AnthropicClient) GetModels() []string {
	return []string{
		"claude-3-5-sonnet-20241022",
		"claude-3-opus-20240229",
		"claude-3-sonnet-20240229",
		"claude-3-haiku-20240307",
	}
}

// ValidateModel checks if a model is supported
func (c *AnthropicClient) ValidateModel(model string) bool {
	models := c.GetModels()
	for _, m := range models {
		if m == model {
			return true
		}
	}
	return false
}

// BuildMessages converts simple prompts to Claude message format
func (c *AnthropicClient) BuildMessages(userPrompt string) []AnthropicMessage {
	var messages []AnthropicMessage

	if userPrompt != "" {
		messages = append(messages, AnthropicMessage{
			Role:    "user",
			Content: userPrompt,
		})
	}

	return messages
}

// CreateSimpleRequest creates a simple message request
func (c *AnthropicClient) CreateSimpleRequest(model, systemPrompt, userPrompt string, temperature float64, maxTokens int) AnthropicRequest {
	messages := c.BuildMessages(userPrompt)

	req := AnthropicRequest{
		Model:     model,
		MaxTokens: maxTokens,
		Messages:  messages,
	}

	if systemPrompt != "" {
		req.System = systemPrompt
	}

	if temperature >= 0 {
		req.Temperature = &temperature
	}

	if maxTokens <= 0 {
		req.MaxTokens = 1000 // Default max tokens
	}

	return req
}

// ExtractTextContent extracts text content from the response
func (c *AnthropicClient) ExtractTextContent(response *AnthropicResponse) string {
	if len(response.Content) == 0 {
		return ""
	}

	var text string
	for _, block := range response.Content {
		if block.Type == "text" {
			text += block.Text
		}
	}

	return text
}

// EstimateTokens provides a rough estimate of token count
func (c *AnthropicClient) EstimateTokens(text string) int {
	// Rough estimation: ~4 characters per token for English text
	return len(text) / 4
}
