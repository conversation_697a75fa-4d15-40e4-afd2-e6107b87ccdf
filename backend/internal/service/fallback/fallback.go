package fallback

import (
	"context"
	"fmt"
	"log"
	"time"
)

// Strategy defines the fallback strategy
type Strategy string

const (
	StrategyMock     Strategy = "mock"
	StrategyCache    Strategy = "cache"
	StrategyAlternate Strategy = "alternate"
	StrategyFail     Strategy = "fail"
)

// Config holds fallback configuration
type Config struct {
	Strategy    Strategy      `json:"strategy"`
	Timeout     time.Duration `json:"timeout"`
	EnableCache bool          `json:"enable_cache"`
	CacheTTL    time.Duration `json:"cache_ttl"`
}

// DefaultConfig returns a default fallback configuration
func DefaultConfig() *Config {
	return &Config{
		Strategy:    StrategyMock,
		Timeout:     30 * time.Second,
		EnableCache: true,
		CacheTTL:    5 * time.Minute,
	}
}

// Handler handles fallback logic
type Handler struct {
	config *Config
	cache  map[string]*CacheEntry
}

// CacheEntry represents a cached response
type CacheEntry struct {
	Data      interface{}
	Timestamp time.Time
	TTL       time.Duration
}

// IsExpired checks if the cache entry is expired
func (e *CacheEntry) IsExpired() bool {
	return time.Since(e.Timestamp) > e.TTL
}

// New creates a new fallback handler
func New(config *Config) *Handler {
	if config == nil {
		config = DefaultConfig()
	}
	return &Handler{
		config: config,
		cache:  make(map[string]*CacheEntry),
	}
}

// Execute executes the primary function with fallback support
func (h *Handler) Execute(ctx context.Context, key string, primary func() (interface{}, error), fallback func() (interface{}, error)) (interface{}, error) {
	// Try to get from cache first if enabled
	if h.config.EnableCache {
		if cached := h.getFromCache(key); cached != nil {
			log.Printf("Fallback: Using cached response for key: %s", key)
			return cached, nil
		}
	}
	
	// Create a context with timeout
	timeoutCtx, cancel := context.WithTimeout(ctx, h.config.Timeout)
	defer cancel()
	
	// Try primary function
	result, err := h.executeWithTimeout(timeoutCtx, primary)
	if err == nil {
		// Cache successful result
		if h.config.EnableCache {
			h.setCache(key, result)
		}
		return result, nil
	}
	
	log.Printf("Fallback: Primary function failed for key %s: %v", key, err)
	
	// Execute fallback strategy
	switch h.config.Strategy {
	case StrategyMock:
		return h.executeMockFallback(key, err)
		
	case StrategyCache:
		return h.executeCacheFallback(key, err)
		
	case StrategyAlternate:
		if fallback != nil {
			log.Printf("Fallback: Executing alternate function for key: %s", key)
			result, fallbackErr := h.executeWithTimeout(timeoutCtx, fallback)
			if fallbackErr == nil {
				if h.config.EnableCache {
					h.setCache(key, result)
				}
				return result, nil
			}
			log.Printf("Fallback: Alternate function also failed for key %s: %v", key, fallbackErr)
		}
		return nil, fmt.Errorf("all fallback strategies failed: primary error: %w", err)
		
	case StrategyFail:
		return nil, fmt.Errorf("fallback disabled, primary error: %w", err)
		
	default:
		return h.executeMockFallback(key, err)
	}
}

// executeWithTimeout executes a function with timeout
func (h *Handler) executeWithTimeout(ctx context.Context, fn func() (interface{}, error)) (interface{}, error) {
	resultChan := make(chan interface{}, 1)
	errorChan := make(chan error, 1)
	
	go func() {
		result, err := fn()
		if err != nil {
			errorChan <- err
		} else {
			resultChan <- result
		}
	}()
	
	select {
	case result := <-resultChan:
		return result, nil
	case err := <-errorChan:
		return nil, err
	case <-ctx.Done():
		return nil, fmt.Errorf("function execution timeout: %w", ctx.Err())
	}
}

// executeMockFallback executes mock fallback
func (h *Handler) executeMockFallback(key string, originalErr error) (interface{}, error) {
	log.Printf("Fallback: Using mock response for key: %s", key)
	
	// Generate mock response based on key
	mockResponse := map[string]interface{}{
		"status":        "fallback",
		"message":       "Service temporarily unavailable, using mock response",
		"original_error": originalErr.Error(),
		"timestamp":     time.Now().Format(time.RFC3339),
		"key":           key,
	}
	
	return mockResponse, nil
}

// executeCacheFallback executes cache fallback (returns stale cache if available)
func (h *Handler) executeCacheFallback(key string, originalErr error) (interface{}, error) {
	// Try to get stale cache entry
	if entry, exists := h.cache[key]; exists {
		log.Printf("Fallback: Using stale cached response for key: %s", key)
		return entry.Data, nil
	}
	
	// No cache available, fall back to mock
	return h.executeMockFallback(key, originalErr)
}

// getFromCache retrieves a valid cache entry
func (h *Handler) getFromCache(key string) interface{} {
	entry, exists := h.cache[key]
	if !exists {
		return nil
	}
	
	if entry.IsExpired() {
		delete(h.cache, key)
		return nil
	}
	
	return entry.Data
}

// setCache stores data in cache
func (h *Handler) setCache(key string, data interface{}) {
	h.cache[key] = &CacheEntry{
		Data:      data,
		Timestamp: time.Now(),
		TTL:       h.config.CacheTTL,
	}
}

// ClearCache clears all cached entries
func (h *Handler) ClearCache() {
	h.cache = make(map[string]*CacheEntry)
}

// ClearExpiredCache removes expired cache entries
func (h *Handler) ClearExpiredCache() {
	for key, entry := range h.cache {
		if entry.IsExpired() {
			delete(h.cache, key)
		}
	}
}

// GetCacheStats returns cache statistics
func (h *Handler) GetCacheStats() map[string]interface{} {
	totalEntries := len(h.cache)
	expiredEntries := 0
	
	for _, entry := range h.cache {
		if entry.IsExpired() {
			expiredEntries++
		}
	}
	
	return map[string]interface{}{
		"total_entries":   totalEntries,
		"expired_entries": expiredEntries,
		"valid_entries":   totalEntries - expiredEntries,
	}
}

// HealthCheck performs a health check on the fallback system
func (h *Handler) HealthCheck() map[string]interface{} {
	stats := h.GetCacheStats()
	
	return map[string]interface{}{
		"status":    "healthy",
		"strategy":  string(h.config.Strategy),
		"cache_enabled": h.config.EnableCache,
		"cache_stats":   stats,
		"timeout":       h.config.Timeout.String(),
	}
}
