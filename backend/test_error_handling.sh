#!/bin/bash

echo "Testing Enhanced Error Handling and Retry Mechanisms..."

# Test 1: Normal LLM workflow (should work with fallback)
echo "1. Testing normal LLM workflow with fallback..."
WORKFLOW_ID=$(curl -s -X POST http://localhost:8080/api/v1/workflows \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Error Handling Test",
    "description": "Test enhanced error handling with retry and fallback",
    "nodes": [
      {
        "key": "entry",
        "name": "Start",
        "type": "entry",
        "description": "Entry point",
        "config": {},
        "position": {"x": 100, "y": 100}
      },
      {
        "key": "llm",
        "name": "AI Assistant",
        "type": "llm",
        "description": "LLM with error handling",
        "config": {
          "model": "gpt-4o-mini",
          "system_prompt": "You are a helpful AI assistant.",
          "user_prompt": "Please respond to: {{text}}",
          "temperature": 0.7,
          "max_tokens": 200
        },
        "position": {"x": 300, "y": 100}
      },
      {
        "key": "exit",
        "name": "End",
        "type": "exit",
        "description": "Exit point",
        "config": {},
        "position": {"x": 500, "y": 100}
      }
    ],
    "connections": [
      {
        "from_node": "entry",
        "to_node": "llm"
      },
      {
        "from_node": "llm",
        "to_node": "exit"
      }
    ]
  }' | jq -r '.id')

echo "Created workflow with ID: $WORKFLOW_ID"

# Execute the workflow
echo "2. Executing workflow (should use fallback since no real API keys)..."
EXECUTION_RESULT=$(curl -s -X POST http://localhost:8080/api/v1/workflows/$WORKFLOW_ID/execute \
  -H "Content-Type: application/json" \
  -d '{
    "input": {
      "text": "What is artificial intelligence?"
    }
  }')

echo "Execution result:"
echo "$EXECUTION_RESULT" | jq '.'

# Test 2: Multiple rapid executions to test caching
echo ""
echo "3. Testing caching with multiple rapid executions..."
for i in {1..3}; do
  echo "Execution $i:"
  curl -s -X POST http://localhost:8080/api/v1/workflows/$WORKFLOW_ID/execute \
    -H "Content-Type: application/json" \
    -d '{
      "input": {
        "text": "What is machine learning?"
      }
    }' | jq '.node_executions[] | select(.node_type == "llm") | {status, output: .output.response}'
  echo ""
done

echo "Error handling test completed!"
