version: '3.8'

services:
  workflow-engine:
    build: .
    ports:
      - "8080:8080"
    environment:
      - ENV=production
      - PORT=8080
    volumes:
      - workflow_data:/app/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Optional: Add Redis for caching and session storage
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    command: redis-server --appendonly yes

  # Optional: Add PostgreSQL for persistent storage
  postgres:
    image: postgres:15-alpine
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=workflow_engine
      - POSTGRES_USER=workflow
      - POSTGRES_PASSWORD=workflow_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped

volumes:
  workflow_data:
  redis_data:
  postgres_data:
